using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using GameServer.ExcelData;
using GameServer.ORM;

namespace ExcelToData
{
	/// <summary>
	/// ComfyUI服务器信息
	/// </summary>
	public class ComfyUIServer : EntityBase
	{
		/// <summary>
		/// ORM Table ID
		/// </summary>
		public uint dbID 
		{
			get;set;
		}

		/// <summary>
		/// 服务器ID
		/// </summary>
		public string id
		{
			get;set;
		}

		/// <summary>
		/// 服务器名称
		/// </summary>
		public string? serverName
		{
			get;set;
		}

		/// <summary>
		/// 服务器地址
		/// </summary>
		public string? serverUrl
		{
			get;set;
		}

		/// <summary>
		/// 服务器端口
		/// </summary>
		public int port
		{
			get;set;
		}

		/// <summary>
		/// 服务器状态 (0:离线, 1:在线, 2:忙碌, 3:维护)
		/// </summary>
		public int status
		{
			get;set;
		}

		/// <summary>
		/// 最大并发任务数
		/// </summary>
		public int maxConcurrentTasks
		{
			get;set;
		}

		/// <summary>
		/// 当前运行任务数
		/// </summary>
		public int currentTasks
		{
			get;set;
		}

		/// <summary>
		/// 支持的workflow类型列表 (JSON格式)
		/// </summary>
		public string? supportedWorkflows
		{
			get;set;
		}

		/// <summary>
		/// 服务器描述
		/// </summary>
		public string? description
		{
			get;set;
		}

		/// <summary>
		/// 最后心跳时间
		/// </summary>
		public DateTime? lastHeartbeat
		{
			get;set;
		}



		public static string[] propList = { "id","serverName","serverUrl","port","status","maxConcurrentTasks","currentTasks","supportedWorkflows","description","lastHeartbeat" };

		public static ComfyUIServer add(ComfyUIServer a, ComfyUIServer b, uint start, uint end, ComfyUIServer limit = null) {
			if(a == null || b == null) return null;
			ComfyUIServer result = new ComfyUIServer();
			for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
				string prop = propList[i];
				switch(prop) {
					case "id":
						result.id = a.id ?? b.id;
						break;
					case "serverName":
						result.serverName = a.serverName ?? b.serverName;
						break;
					case "serverUrl":
						result.serverUrl = a.serverUrl ?? b.serverUrl;
						break;
					case "port":
						result.port = a.port != 0 ? a.port : b.port;
						break;
					case "status":
						result.status = a.status != 0 ? a.status : b.status;
						break;
					case "maxConcurrentTasks":
						result.maxConcurrentTasks = a.maxConcurrentTasks != 0 ? a.maxConcurrentTasks : b.maxConcurrentTasks;
						break;
					case "currentTasks":
						result.currentTasks = a.currentTasks != 0 ? a.currentTasks : b.currentTasks;
						break;
					case "supportedWorkflows":
						result.supportedWorkflows = a.supportedWorkflows ?? b.supportedWorkflows;
						break;
					case "description":
						result.description = a.description ?? b.description;
						break;
					case "lastHeartbeat":
						result.lastHeartbeat = a.lastHeartbeat ?? b.lastHeartbeat;
						break;
				}
			}
			return result;
		}

		public static List<ComfyUIServer> readAllData()
		{
			return new List<ComfyUIServer>();
		}

		public List<ComfyUIServer> getAllDataToList()
		{
			return ORMTables.Instance.ComfyUIServers.ToList();
		}

		public List<ComfyUIServer> getDataToKey(string fieldName)
		{
			var query = ORMTables.Instance.ComfyUIServers.AsQueryable();
			switch(fieldName.ToLower())
			{
				case "id":
					return query.Where(x => x.id != null).ToList();
				case "servername":
					return query.Where(x => x.serverName != null).ToList();
				case "serverurl":
					return query.Where(x => x.serverUrl != null).ToList();
				case "status":
					return query.Where(x => x.status != 0).ToList();
				default:
					return query.ToList();
			}
		}
	}

	/// <summary>
	/// ComfyUI工作流配置
	/// </summary>
	public class ComfyUIWorkflow : EntityBase
	{
		/// <summary>
		/// ORM Table ID
		/// </summary>
		public uint dbID 
		{
			get;set;
		}

		/// <summary>
		/// 工作流ID
		/// </summary>
		public string id
		{
			get;set;
		}

		/// <summary>
		/// 工作流名称
		/// </summary>
		public string? workflowName
		{
			get;set;
		}

		/// <summary>
		/// 工作流JSON配置
		/// </summary>
		public string? workflowJson
		{
			get;set;
		}

		/// <summary>
		/// 工作流类型
		/// </summary>
		public string? workflowType
		{
			get;set;
		}

		/// <summary>
		/// 工作流描述
		/// </summary>
		public string? description
		{
			get;set;
		}

		/// <summary>
		/// 工作流版本
		/// </summary>
		public string? workflowVersion
		{
			get;set;
		}

		/// <summary>
		/// 创建者
		/// </summary>
		public string? creator
		{
			get;set;
		}

		/// <summary>
		/// 是否启用
		/// </summary>
		public bool isEnabled
		{
			get;set;
		}



		public static string[] propList = { "id","workflowName","workflowJson","workflowType","description","workflowVersion","creator","isEnabled" };

		public static List<ComfyUIWorkflow> readAllData()
		{
			return new List<ComfyUIWorkflow>();
		}

		public List<ComfyUIWorkflow> getAllDataToList()
		{
			return ORMTables.Instance.ComfyUIWorkflows.ToList();
		}

		public List<ComfyUIWorkflow> getDataToKey(string fieldName)
		{
			var query = ORMTables.Instance.ComfyUIWorkflows.AsQueryable();
			switch(fieldName.ToLower())
			{
				case "id":
					return query.Where(x => x.id != null).ToList();
				case "workflowname":
					return query.Where(x => x.workflowName != null).ToList();
				case "workflowtype":
					return query.Where(x => x.workflowType != null).ToList();
				case "isenabled":
					return query.Where(x => x.isEnabled).ToList();
				default:
					return query.ToList();
			}
		}
	}

	/// <summary>
	/// ComfyUI任务信息
	/// </summary>
	public class ComfyUITask : EntityBase
	{
		/// <summary>
		/// ORM Table ID
		/// </summary>
		public uint dbID
		{
			get;set;
		}

		/// <summary>
		/// 任务ID
		/// </summary>
		public string id
		{
			get;set;
		}

		/// <summary>
		/// 服务器ID
		/// </summary>
		public string? serverId
		{
			get;set;
		}

		/// <summary>
		/// 工作流ID
		/// </summary>
		public string? workflowId
		{
			get;set;
		}

		/// <summary>
		/// 任务名称
		/// </summary>
		public string? taskName
		{
			get;set;
		}

		/// <summary>
		/// 任务状态 (0:等待, 1:运行中, 2:完成, 3:失败, 4:取消)
		/// </summary>
		public int status
		{
			get;set;
		}

		/// <summary>
		/// 当前执行节点
		/// </summary>
		public string? currentNode
		{
			get;set;
		}

		/// <summary>
		/// 当前节点名称
		/// </summary>
		public string? currentNodeName
		{
			get;set;
		}

		/// <summary>
		/// 当前节点输入
		/// </summary>
		public string? currentNodeInput
		{
			get;set;
		}

		/// <summary>
		/// 当前节点输出
		/// </summary>
		public string? currentNodeOutput
		{
			get;set;
		}

		/// <summary>
		/// 任务进度 (0-100)
		/// </summary>
		public int progress
		{
			get;set;
		}

		/// <summary>
		/// 队列位置
		/// </summary>
		public int queuePosition
		{
			get;set;
		}

		/// <summary>
		/// 优先级
		/// </summary>
		public int priority
		{
			get;set;
		}

		/// <summary>
		/// 创建者
		/// </summary>
		public string? creator
		{
			get;set;
		}

		/// <summary>
		/// 错误信息
		/// </summary>
		public string? errorMessage
		{
			get;set;
		}

		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime? startTime
		{
			get;set;
		}

		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime? endTime
		{
			get;set;
		}

		/// <summary>
		/// 任务日志 (JSON格式数组)
		/// </summary>
		public string? Logs
		{
			get;set;
		}

		/// <summary>
		/// 任务文件 (JSON格式数组)
		/// </summary>
		public string? Files
		{
			get;set;
		}



		public static string[] propList = { "id","serverId","workflowId","taskName","status","currentNode","currentNodeName","currentNodeInput","currentNodeOutput","progress","queuePosition","priority","creator","errorMessage","startTime","endTime","Logs","Files" };

		public static List<ComfyUITask> readAllData()
		{
			return new List<ComfyUITask>();
		}

		public List<ComfyUITask> getAllDataToList()
		{
			return ORMTables.Instance.ComfyUITasks.ToList();
		}

		public List<ComfyUITask> getDataToKey(string fieldName)
		{
			var query = ORMTables.Instance.ComfyUITasks.AsQueryable();
			switch(fieldName.ToLower())
			{
				case "id":
					return query.Where(x => x.id != null).ToList();
				case "serverid":
					return query.Where(x => x.serverId != null).ToList();
				case "workflowid":
					return query.Where(x => x.workflowId != null).ToList();
				case "status":
					return query.Where(x => x.status != 0).ToList();
				case "creator":
					return query.Where(x => x.creator != null).ToList();
				default:
					return query.ToList();
			}
		}
	}


}
