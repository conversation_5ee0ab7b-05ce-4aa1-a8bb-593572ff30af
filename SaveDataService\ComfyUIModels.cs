using System;
using System.Collections.Generic;
using SaveDataService;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// 工作流信息
    /// </summary>
    public class WorkflowInfo
    {
        /// <summary>
        /// 工作流JSON
        /// </summary>
        public string WorkflowJson { get; set; } = "";

        /// <summary>
        /// 节点列表
        /// </summary>
        public List<WorkflowNode> Nodes { get; set; } = new List<WorkflowNode>();

        /// <summary>
        /// 输入参数
        /// </summary>
        public List<WorkflowParameter> InputParameters { get; set; } = new List<WorkflowParameter>();

        /// <summary>
        /// 输出参数
        /// </summary>
        public List<WorkflowParameter> OutputParameters { get; set; } = new List<WorkflowParameter>();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Error { get; set; } = "";
    }

    /// <summary>
    /// 工作流节点
    /// </summary>
    public class WorkflowNode
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; } = "";

        /// <summary>
        /// 节点类型
        /// </summary>
        public string NodeType { get; set; } = "";

        /// <summary>
        /// 节点标题
        /// </summary>
        public string NodeTitle { get; set; } = "";

        /// <summary>
        /// 输入参数
        /// </summary>
        public Dictionary<string, object> Inputs { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 输出列表
        /// </summary>
        public List<string> Outputs { get; set; } = new List<string>();
    }

    /// <summary>
    /// 工作流参数
    /// </summary>
    public class WorkflowParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 参数类型
        /// </summary>
        public string Type { get; set; } = "";

        /// <summary>
        /// 参数值
        /// </summary>
        public object Value { get; set; } = "";

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; } = false;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// 任务执行上下文
    /// </summary>
    public class TaskExecutionContext
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = "";

        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = "";

        /// <summary>
        /// 任务对象
        /// </summary>
        public ComfyUITask Task { get; set; }

        /// <summary>
        /// 工作流对象
        /// </summary>
        public ComfyUIWorkflow Workflow { get; set; }

        /// <summary>
        /// 工作流信息
        /// </summary>
        public WorkflowInfo WorkflowInfo { get; set; }

        /// <summary>
        /// 执行日志
        /// </summary>
        public List<TaskExecutionStep> ExecutionLog { get; set; } = new List<TaskExecutionStep>();

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 当前节点索引
        /// </summary>
        public int CurrentNodeIndex { get; set; }

        /// <summary>
        /// 总节点数
        /// </summary>
        public int TotalNodes { get; set; }

        /// <summary>
        /// 当前节点ID
        /// </summary>
        public string CurrentNodeId { get; set; } = "";

        /// <summary>
        /// 当前节点名称
        /// </summary>
        public string CurrentNodeName { get; set; } = "";

        /// <summary>
        /// 进度百分比
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public TaskExecutionStatus Status { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 任务执行步骤
    /// </summary>
    public class TaskExecutionStep
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; } = "";

        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; } = "";

        /// <summary>
        /// 输入数据
        /// </summary>
        public string Input { get; set; } = "";

        /// <summary>
        /// 输出数据
        /// </summary>
        public string Output { get; set; } = "";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = "";
    }

    /// <summary>
    /// 任务执行状态枚举
    /// </summary>
    public enum TaskExecutionStatus
    {
        /// <summary>
        /// 已初始化
        /// </summary>
        Initialized = 0,

        /// <summary>
        /// 运行中
        /// </summary>
        Running = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// 任务执行报告
    /// </summary>
    public class TaskExecutionReport
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = "";

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = "";

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 进度
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 执行日志 (JSON格式)
        /// </summary>
        public List<object> Logs { get; set; } = new List<object>();

        /// <summary>
        /// 相关文件 (JSON格式)
        /// </summary>
        public List<object> Files { get; set; } = new List<object>();

        /// <summary>
        /// 输入文件 (JSON格式)
        /// </summary>
        public List<object> InputFiles { get; set; } = new List<object>();

        /// <summary>
        /// 输出文件 (JSON格式)
        /// </summary>
        public List<object> OutputFiles { get; set; } = new List<object>();
    }

    /// <summary>
    /// ComfyUI服务器状态信息
    /// </summary>
    public class ComfyUIServerStatus
    {
        /// <summary>
        /// 服务器ID
        /// </summary>
        public string ServerId { get; set; } = "";

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName { get; set; } = "";

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; } = "";

        /// <summary>
        /// 当前任务数
        /// </summary>
        public int CurrentTasks { get; set; }

        /// <summary>
        /// 最大任务数
        /// </summary>
        public int MaxTasks { get; set; }

        /// <summary>
        /// 队列中的任务
        /// </summary>
        public List<ComfyUITask> QueueTasks { get; set; } = new List<ComfyUITask>();

        /// <summary>
        /// 运行中的任务
        /// </summary>
        public List<ComfyUITask> RunningTasks { get; set; } = new List<ComfyUITask>();

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime? LastHeartbeat { get; set; }
    }
}
