﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>

    <InvariantGlobalization>true</InvariantGlobalization>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
     <PublishAot>false</PublishAot>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aliyun.OSS.SDK.NetCore" Version="2.14.1" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="KubernetesClient" Version="16.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Server.Kestrel" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.WebSockets" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Features" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.54.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0-preview.3.efcore.9.0.0" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Loki.YetAnother" Version="3.0.7" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.31" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="TencentCloudSDK.Common" Version="3.0.1231" />
    <PackageReference Include="TencentCloudSDK.Tke" Version="3.0.1231" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Res\baseExcel\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AISdk\AISdk.csproj" />
    <ProjectReference Include="..\ORMTools\ORMTools.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Res\baseExcel\Account.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\ErrorInfo.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\ItemBase.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\ItemData.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\ServerLog.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\ServerUserData.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\serverVar.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\SeverConfig.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\SeverData.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\SysActive.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\TimeEvent.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Res\baseExcel\UserVar.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="api-test.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="upload.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="test.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
