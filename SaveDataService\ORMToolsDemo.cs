using System;
using ORMTools;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// ORMTools使用演示类
    /// </summary>
    public class ORMToolsDemo
    {
        /// <summary>
        /// 演示如何使用ORMTools生成CSV文件
        /// </summary>
        public static void RunDemo()
        {
            Console.WriteLine("=== ORMTools 演示开始 ===");
            
            try
            {
                // 演示1：分析Account实体类
                Console.WriteLine("\n1. 分析Account实体类...");
                string accountCsvPath = ORMEntityAnalyzer.GenerateEntityCsv(typeof(Account), ".");
                Console.WriteLine($"Account CSV文件已生成: {accountCsvPath}");

                // 演示2：分析ErrorInfo实体类
                Console.WriteLine("\n2. 分析ErrorInfo实体类...");
                string errorInfoCsvPath = ORMEntityAnalyzer.GenerateEntityCsv(typeof(ErrorInfo), ".");
                Console.WriteLine($"ErrorInfo CSV文件已生成: {errorInfoCsvPath}");

                // 演示3：分析TimeEvent实体类
                Console.WriteLine("\n3. 分析TimeEvent实体类...");
                string timeEventCsvPath = ORMEntityAnalyzer.GenerateEntityCsv(typeof(TimeEvent), ".");
                Console.WriteLine($"TimeEvent CSV文件已生成: {timeEventCsvPath}");

                // 演示4：分析SeverData实体类
                Console.WriteLine("\n4. 分析SeverData实体类...");
                string severDataCsvPath = ORMEntityAnalyzer.GenerateEntityCsv(typeof(SeverData), ".");
                Console.WriteLine($"SeverData CSV文件已生成: {severDataCsvPath}");

                Console.WriteLine("\n=== 所有CSV文件生成完成 ===");
                Console.WriteLine("CSV文件格式说明：");
                Console.WriteLine("- 第一行：属性名称");
                Console.WriteLine("- 第二行：数据类型（数组/List显示为如string[]格式）");
                Console.WriteLine("- 第三行：注释内容");
                
                // 显示生成的文件列表
                Console.WriteLine("\n生成的文件列表：");
                Console.WriteLine($"- {accountCsvPath}");
                Console.WriteLine($"- {errorInfoCsvPath}");
                Console.WriteLine($"- {timeEventCsvPath}");
                Console.WriteLine($"- {severDataCsvPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
            }
            
            Console.WriteLine("\n=== ORMTools 演示结束 ===");
        }

        /// <summary>
        /// 分析指定类型并生成CSV
        /// </summary>
        /// <param name="entityType">要分析的实体类型</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeEntity(Type entityType, string outputPath = ".")
        {
            try
            {
                Console.WriteLine($"\n正在分析实体类: {entityType.Name}");
                string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputPath);
                Console.WriteLine($"CSV文件已生成: {csvPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析实体类 {entityType.Name} 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量分析多个实体类
        /// </summary>
        /// <param name="entityTypes">实体类型数组</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeMultipleEntities(Type[] entityTypes, string outputPath = ".")
        {
            Console.WriteLine($"\n开始批量分析 {entityTypes.Length} 个实体类...");
            
            int successCount = 0;
            int failCount = 0;
            
            foreach (var entityType in entityTypes)
            {
                try
                {
                    AnalyzeEntity(entityType, outputPath);
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"分析 {entityType.Name} 失败: {ex.Message}");
                    failCount++;
                }
            }
            
            Console.WriteLine($"\n批量分析完成: 成功 {successCount} 个，失败 {failCount} 个");
        }
    }
}
