using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;

// 定义实体模型
public class Customer
{
    public int Id { get; set; }
    public string Name { get; set; }
    public decimal Balance { get; set; }
}

public class Order
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public decimal Amount { get; set; }
    public DateTime OrderDate { get; set; }
    public Customer Customer { get; set; }
}

// 定义DbContext
public class AppDbContext : DbContext
{
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Order> Orders { get; set; }

    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // 配置模型关系
        modelBuilder.Entity<Order>()
            .HasOne(o => o.Customer)
            .WithMany()
            .HasForeignKey(o => o.CustomerId);
    }
}

public class TransactionService
{
    private readonly AppDbContext _dbContext;

    public TransactionService(AppDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    // 基本事务示例
    public void BasicTransaction()
    {
        // 开始事务
        using var transaction = _dbContext.Database.BeginTransaction();

        try
        {
            // 操作1: 添加客户
            var customer = new Customer { Name = "John Doe", Balance = 1000 };
            _dbContext.Customers.Add(customer);
            _dbContext.SaveChanges(); // 保存更改但不提交事务

            // 操作2: 添加订单
            var order = new Order { CustomerId = customer.Id, Amount = 100, OrderDate = DateTime.Now };
            _dbContext.Orders.Add(order);
            _dbContext.SaveChanges(); // 保存更改但不提交事务

            // 提交事务
            transaction.Commit();
            Console.WriteLine("事务提交成功");
        }
        catch (Exception ex)
        {
            // 发生异常时回滚事务
            transaction.Rollback();
            Console.WriteLine($"事务回滚: {ex.Message}");
            throw; // 可以选择重新抛出异常或处理它
        }
    }

    // 嵌套事务示例
    public void NestedTransaction()
    {
        // 外层事务
        using var outerTransaction = _dbContext.Database.BeginTransaction();

        try
        {
            // 操作1: 添加客户
            var customer = new Customer { Name = "Jane Smith", Balance = 2000 };
            _dbContext.Customers.Add(customer);
            _dbContext.SaveChanges();

            // 内层事务
            using var innerTransaction = _dbContext.Database.BeginTransaction();
            try
            {
                // 操作2: 添加订单
                var order = new Order { CustomerId = customer.Id, Amount = 200, OrderDate = DateTime.Now };
                _dbContext.Orders.Add(order);
                _dbContext.SaveChanges();

                // 提交内层事务
                innerTransaction.Commit();
            }
            catch
            {
                innerTransaction.Rollback();
                throw;
            }

            // 操作3: 更新客户余额
            customer.Balance -= 200;
            _dbContext.SaveChanges();

            // 提交外层事务
            outerTransaction.Commit();
            Console.WriteLine("嵌套事务提交成功");
        }
        catch (Exception ex)
        {
            outerTransaction.Rollback();
            Console.WriteLine($"嵌套事务回滚: {ex.Message}");
            throw;
        }
    }

    // 跨上下文事务示例(需要分布式事务)
    public void CrossContextTransaction(AppDbContext secondDbContext)
    {
        // 创建事务管理器
        using var transaction = _dbContext.Database.BeginTransaction();

        try
        {
            // 在第一个上下文中操作
            var customer = new Customer { Name = "Bob Johnson", Balance = 3000 };
            _dbContext.Customers.Add(customer);
            _dbContext.SaveChanges();

            // 在第二个上下文中操作
            secondDbContext.Database.UseTransaction(transaction.GetDbTransaction());
            var order = new Order { CustomerId = customer.Id, Amount = 300, OrderDate = DateTime.Now };
            secondDbContext.Orders.Add(order);
            secondDbContext.SaveChanges();

            // 提交事务
            transaction.Commit();
            Console.WriteLine("跨上下文事务提交成功");
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            Console.WriteLine($"跨上下文事务回滚: {ex.Message}");
            throw;
        }
    }

    // 保存点示例(部分回滚)
    public void SavePointTransaction()
    {
        using var transaction = _dbContext.Database.BeginTransaction();

        try
        {
            // 操作1: 添加客户
            var customer = new Customer { Name = "Alice Brown", Balance = 4000 };
            _dbContext.Customers.Add(customer);
            _dbContext.SaveChanges();

            // 创建保存点
            transaction.CreateSavepoint("AfterCustomerCreated");

            try
            {
                // 操作2: 添加订单(可能失败)
                var order = new Order { CustomerId = customer.Id, Amount = -100, OrderDate = DateTime.Now }; // 故意设置负金额
                _dbContext.Orders.Add(order);
                _dbContext.SaveChanges();

                transaction.Commit();
            }
            catch (Exception ex)
            {
                // 回滚到保存点，只撤销订单操作
                transaction.RollbackToSavepoint("AfterCustomerCreated");
                Console.WriteLine($"回滚到保存点，保留客户: {ex.Message}");

                // 可以继续其他操作
                customer.Balance = 5000;
                _dbContext.SaveChanges();

                transaction.Commit();
                Console.WriteLine("保存点事务部分提交成功");
            }
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            Console.WriteLine($"保存点事务完全回滚: {ex.Message}");
            throw;
        }
    }

    // 异步事务示例
    public async Task AsyncTransaction()
    {
        // 开始异步事务
        await using var transaction = await _dbContext.Database.BeginTransactionAsync();

        try
        {
            // 异步操作1: 添加客户
            var customer = new Customer { Name = "Async User", Balance = 6000 };
            _dbContext.Customers.Add(customer);
            await _dbContext.SaveChangesAsync();

            // 异步操作2: 添加订单
            var order = new Order { CustomerId = customer.Id, Amount = 600, OrderDate = DateTime.Now };
            _dbContext.Orders.Add(order);
            await _dbContext.SaveChangesAsync();

            // 异步提交
            await transaction.CommitAsync();
            Console.WriteLine("异步事务提交成功");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            Console.WriteLine($"异步事务回滚: {ex.Message}");
            throw;
        }
    }

    // 事务隔离级别示例
    public void IsolationLevelTransaction()
    {
        // 使用可序列化隔离级别(最高隔离级别，防止脏读、不可重复读和幻读)
        using var transaction = _dbContext.Database.BeginTransaction(System.Data.IsolationLevel.Serializable);

        try
        {
            // 读取数据(在可序列化级别下，这些数据会被锁定)
            var customer = _dbContext.Customers.FirstOrDefault(c => c.Name == "John Doe");
            if (customer == null)
            {
                customer = new Customer { Name = "John Doe", Balance = 7000 };
                _dbContext.Customers.Add(customer);
            }

            // 更新数据
            customer.Balance += 1000;
            _dbContext.SaveChanges();

            transaction.Commit();
            Console.WriteLine("隔离级别事务提交成功");
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            Console.WriteLine($"隔离级别事务回滚: {ex.Message}");
            throw;
        }
    }

    // 事务重试策略示例
    public void RetryPolicyTransaction(int maxRetries = 3)
    {
        int retryCount = 0;
        bool succeeded = false;

        while (!succeeded && retryCount < maxRetries)
        {
            using var transaction = _dbContext.Database.BeginTransaction();

            try
            {
                // 模拟并发冲突
                var customer = _dbContext.Customers.OrderBy(c => c.Id).First();
                customer.Balance += 100;

                // 模拟长时间运行的操作
                Thread.Sleep(2000);

                _dbContext.SaveChanges();
                transaction.Commit();
                succeeded = true;
                Console.WriteLine($"事务成功(尝试次数: {retryCount + 1})");
            }
            catch (DbUpdateConcurrencyException ex)
            {
                transaction.Rollback();
                retryCount++;
                Console.WriteLine($"并发冲突，重试 {retryCount}/{maxRetries}: {ex.Message}");

                if (retryCount >= maxRetries)
                    throw;

                // 等待一段时间再重试
                Thread.Sleep(1000);
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                Console.WriteLine($"事务失败(不重试): {ex.Message}");
                throw;
            }
        }
    }
    public static void demo()
    {
        // 设置依赖注入
        var serviceProvider = new ServiceCollection()
            //.AddDbContext<AppDbContext>(options =>
            //    options.UseMySql("Server=(localdb)\\mssqllocaldb;Database=EFTransactions;Trusted_Connection=True;"))
            //.AddScoped<TransactionService>()
            .BuildServiceProvider();

        // 确保数据库创建
        using (var scope = serviceProvider.CreateScope())
        {
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            dbContext.Database.EnsureCreated();
        }

        // 执行各种事务示例
        using (var scope = serviceProvider.CreateScope())
        {
            var transactionService = scope.ServiceProvider.GetRequiredService<TransactionService>();

            try
            {
                Console.WriteLine("=== 基本事务示例 ===");
                transactionService.BasicTransaction();

                Console.WriteLine("\n=== 嵌套事务示例 ===");
                transactionService.NestedTransaction();

                Console.WriteLine("\n=== 保存点示例 ===");
                transactionService.SavePointTransaction();

                Console.WriteLine("\n=== 异步事务示例 ===");
                transactionService.AsyncTransaction().Wait();

                Console.WriteLine("\n=== 隔离级别示例 ===");
                transactionService.IsolationLevelTransaction();

                Console.WriteLine("\n=== 重试策略示例 ===");
                transactionService.RetryPolicyTransaction();

                Console.WriteLine("\n=== 跨上下文事务示例 ===");
                using var secondScope = serviceProvider.CreateScope();
                var secondDbContext = secondScope.ServiceProvider.GetRequiredService<AppDbContext>();
                transactionService.CrossContextTransaction(secondDbContext);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"主程序捕获异常: {ex.Message}");
            }
        }
    }
}

