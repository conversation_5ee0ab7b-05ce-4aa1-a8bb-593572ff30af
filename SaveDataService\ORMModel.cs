using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using ToolsService;
using System.ComponentModel.DataAnnotations.Schema;
using GameServer.ORM;
using GameServer.ExcelData;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using SaveDataService;


namespace ExcelToData
{

/// <summary>
/// Account
/// </summary>
[Table("accounts")]
public class Account
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 服务器id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 平台id
	/// </summary>
	public uint platformID
	{
		get;set;
	}

	/// <summary>
	/// 用户角色uuids
	/// </summary>
	public string uuids
	{
		get;set;
	}

	/// <summary>
	/// 平台名称
	/// </summary>
	public string? platName
	{
		get;set;
	}

	/// <summary>
	/// 角色编号
	/// </summary>
	public int roleID
	{
		get;set;
	}

	/// <summary>
	/// 角色数量
	/// </summary>
	public int? roleSum
	{
		get;set;
	}

	/// <summary>
	/// 形象id
	/// </summary>
	public string? avatar
	{
		get;set;
	}

	/// <summary>
	/// 真实名字
	/// </summary>
	public string? real_name
	{
		get;set;
	}

	/// <summary>
	/// 昵称/显示名
	/// </summary>
	public string? nickname
	{
		get;set;
	}

	/// <summary>
	/// 用户名字
	/// </summary>
	public string? usrname
	{
		get;set;
	}

	/// <summary>
	/// 用户密码
	/// </summary>
	public string? password
	{
		get;set;
	}

	/// <summary>
	/// 手机号码
	/// </summary>
	public string? mobile
	{
		get;set;
	}

	/// <summary>
	/// 邮箱地址-google
	/// </summary>
	public string? email
	{
		get;set;
	}

	/// <summary>
	/// 生日时间
	/// </summary>
	public long? birth_date
	{
		get;set;
	}

	/// <summary>
	/// 性别
	/// </summary>
	public byte? gender
	{
		get;set;
	}

	/// <summary>
	/// 安全问题
	/// </summary>
	public string[]? security_question
	{
		get;set;
	}

	/// <summary>
	/// 安全答案
	/// </summary>
	public string[]? security_answer
	{
		get;set;
	}

	/// <summary>
	/// 二次验证:0-关闭 1-开启
	/// </summary>
	public bool two_factor_auth
	{
		get;set;
	}

	/// <summary>
	/// 语言偏好
	/// </summary>
	public string? language
	{
		get;set;
	}

	/// <summary>
	/// 时区
	/// </summary>
	public string? timezone
	{
		get;set;
	}

	/// <summary>
	/// 信任的设备唯一码
	/// </summary>
	public string[]? device
	{
		get;set;
	}

	/// <summary>
	/// 删除时间
	/// </summary>
	public long? delete_time
	{
		get;set;
	}

	/// <summary>
	/// 是否删除
	/// </summary>
	public bool deleted
	{
		get;set;
	}

	/// <summary>
	/// ip
	/// </summary>
	public string? ip
	{
		get;set;
	}

	/// <summary>
	/// 城市
	/// </summary>
	public string? city
	{
		get;set;
	}

	/// <summary>
	/// 账户状态
	/// </summary>
	public byte? state
	{
		get;set;
	}

	/// <summary>
	/// 上一次登录ip
	/// </summary>
	public string? lastIP
	{
		get;set;
	}

	/// <summary>
	/// 上一次登录时间
	/// </summary>
	public string? lastLogin
	{
		get;set;
	}

	/// <summary>
	/// 本次登录时间
	/// </summary>
	public string? currentLogin
	{
		get;set;
	}

	/// <summary>
	/// 用户id
	/// </summary>
	public string userId
	{
		get;set;
	}

	/// <summary>
	/// 显示的名称
	/// </summary>
	public string? displayName
	{
		get;set;
	}

	/// <summary>
	/// 姓
	/// </summary>
	public string? familyName
	{
		get;set;
	}

	/// <summary>
	/// 名字
	/// </summary>
	public string? givenName
	{
		get;set;
	}

	/// <summary>
	/// 头像地址
	/// </summary>
	public string? imageUrl
	{
		get;set;
	}

	/// <summary>
	/// idToken
	/// </summary>
	public string idToken
	{
		get;set;
	}

	/// <summary>
	/// serverAuthCode
	/// </summary>
	public string? serverAuthCode
	{
		get;set;
	}

	/// <summary>
	/// OAuth2
	/// </summary>
	public string? accessToken
	{
		get;set;
	}

	/// <summary>
	/// 金币
	/// </summary>
	public int? money
	{
		get;set;
	}

	/// <summary>
	/// 钻石
	/// </summary>
	public int? diamond
	{
		get;set;
	}

	/// <summary>
	/// 游戏天数
	/// </summary>
	public int? playDays
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/Account.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			platformID = (uint)Convert.ChangeType(raw_data[i][1].ToString(), typeof(uint));
		if(raw_data[i][2].ToString() != "")
			uuids = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			platName = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			platName = null;
		if(raw_data[i][4].ToString() != "")
			roleID = (int)Convert.ChangeType(raw_data[i][4].ToString(), typeof(int));
		if(raw_data[i][5].ToString() != "")
			roleSum = (int)Convert.ChangeType(raw_data[i][5].ToString(), typeof(int));
		else
			roleSum = null;
		if(raw_data[i][6].ToString() != "")
			avatar = (string)Convert.ChangeType(raw_data[i][6].ToString(), typeof(string));
		else
			avatar = null;
		if(raw_data[i][7].ToString() != "")
			real_name = (string)Convert.ChangeType(raw_data[i][7].ToString(), typeof(string));
		else
			real_name = null;
		if(raw_data[i][8].ToString() != "")
			nickname = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		else
			nickname = null;
		if(raw_data[i][9].ToString() != "")
			usrname = (string)Convert.ChangeType(raw_data[i][9].ToString(), typeof(string));
		else
			usrname = null;
		if(raw_data[i][10].ToString() != "")
			password = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
		else
			password = null;
		if(raw_data[i][11].ToString() != "")
			mobile = (string)Convert.ChangeType(raw_data[i][11].ToString(), typeof(string));
		else
			mobile = null;
		if(raw_data[i][12].ToString() != "")
			email = (string)Convert.ChangeType(raw_data[i][12].ToString(), typeof(string));
		else
			email = null;
		if(raw_data[i][13].ToString() != "")
			birth_date = (long)Convert.ChangeType(raw_data[i][13].ToString(), typeof(long));
		else
			birth_date = null;
		if(raw_data[i][14].ToString() != "")
			gender = (byte)Convert.ChangeType(raw_data[i][14].ToString(), typeof(byte));
		else
			gender = null;
		if(raw_data[i][15].ToString() != "")
			security_question = (string[])Convert.ChangeType(raw_data[i][15].ToString(), typeof(string[]));
		else
			security_question = null;
		if(raw_data[i][16].ToString() != "")
			security_answer = (string[])Convert.ChangeType(raw_data[i][16].ToString(), typeof(string[]));
		else
			security_answer = null;
		if(raw_data[i][17].ToString() != "")
			two_factor_auth = (bool)Convert.ChangeType(raw_data[i][17].ToString(), typeof(bool));
		if(raw_data[i][18].ToString() != "")
			language = (string)Convert.ChangeType(raw_data[i][18].ToString(), typeof(string));
		else
			language = null;
		if(raw_data[i][19].ToString() != "")
			timezone = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
		else
			timezone = null;
		if(raw_data[i][20].ToString() != "")
			device = (string[])Convert.ChangeType(raw_data[i][20].ToString(), typeof(string[]));
		else
			device = null;
		if(raw_data[i][21].ToString() != "")
			delete_time = (long)Convert.ChangeType(raw_data[i][21].ToString(), typeof(long));
		else
			delete_time = null;
		if(raw_data[i][22].ToString() != "")
			deleted = (bool)Convert.ChangeType(raw_data[i][22].ToString(), typeof(bool));
		if(raw_data[i][23].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][23].ToString(), typeof(string));
		else
			ip = null;
		if(raw_data[i][24].ToString() != "")
			city = (string)Convert.ChangeType(raw_data[i][24].ToString(), typeof(string));
		else
			city = null;
		if(raw_data[i][25].ToString() != "")
			state = (byte)Convert.ChangeType(raw_data[i][25].ToString(), typeof(byte));
		else
			state = null;
		if(raw_data[i][26].ToString() != "")
			lastIP = (string)Convert.ChangeType(raw_data[i][26].ToString(), typeof(string));
		else
			lastIP = null;
		if(raw_data[i][27].ToString() != "")
			lastLogin = (string)Convert.ChangeType(raw_data[i][27].ToString(), typeof(string));
		else
			lastLogin = null;
		if(raw_data[i][28].ToString() != "")
			currentLogin = (string)Convert.ChangeType(raw_data[i][28].ToString(), typeof(string));
		else
			currentLogin = null;
		if(raw_data[i][29].ToString() != "")
			userId = (string)Convert.ChangeType(raw_data[i][29].ToString(), typeof(string));
		if(raw_data[i][30].ToString() != "")
			displayName = (string)Convert.ChangeType(raw_data[i][30].ToString(), typeof(string));
		else
			displayName = null;
		if(raw_data[i][31].ToString() != "")
			familyName = (string)Convert.ChangeType(raw_data[i][31].ToString(), typeof(string));
		else
			familyName = null;
		if(raw_data[i][32].ToString() != "")
			givenName = (string)Convert.ChangeType(raw_data[i][32].ToString(), typeof(string));
		else
			givenName = null;
		if(raw_data[i][33].ToString() != "")
			imageUrl = (string)Convert.ChangeType(raw_data[i][33].ToString(), typeof(string));
		else
			imageUrl = null;
		if(raw_data[i][34].ToString() != "")
			idToken = (string)Convert.ChangeType(raw_data[i][34].ToString(), typeof(string));
		if(raw_data[i][35].ToString() != "")
			serverAuthCode = (string)Convert.ChangeType(raw_data[i][35].ToString(), typeof(string));
		else
			serverAuthCode = null;
		if(raw_data[i][36].ToString() != "")
			accessToken = (string)Convert.ChangeType(raw_data[i][36].ToString(), typeof(string));
		else
			accessToken = null;
		if(raw_data[i][37].ToString() != "")
			money = (int)Convert.ChangeType(raw_data[i][37].ToString(), typeof(int));
		else
			money = null;
		if(raw_data[i][38].ToString() != "")
			diamond = (int)Convert.ChangeType(raw_data[i][38].ToString(), typeof(int));
		else
			diamond = null;
		if(raw_data[i][39].ToString() != "")
			playDays = (int)Convert.ChangeType(raw_data[i][39].ToString(), typeof(int));
		else
			playDays = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"platformID", "uint"},
		{"uuids", "string"},
		{"platName", "string"},
		{"roleID", "int"},
		{"roleSum", "int"},
		{"avatar", "string"},
		{"real_name", "string"},
		{"nickname", "string"},
		{"usrname", "string"},
		{"password", "string"},
		{"mobile", "string"},
		{"email", "string"},
		{"birth_date", "long"},
		{"gender", "byte"},
		{"security_question", "string[]"},
		{"security_answer", "string[]"},
		{"two_factor_auth", "bool"},
		{"language", "string"},
		{"timezone", "string"},
		{"device", "string[]"},
		{"delete_time", "long"},
		{"deleted", "bool"},
		{"ip", "string"},
		{"city", "string"},
		{"state", "byte"},
		{"lastIP", "string"},
		{"lastLogin", "string"},
		{"currentLogin", "string"},
		{"userId", "string"},
		{"displayName", "string"},
		{"familyName", "string"},
		{"givenName", "string"},
		{"imageUrl", "string"},
		{"idToken", "string"},
		{"serverAuthCode", "string"},
		{"accessToken", "string"},
		{"money", "int"},
		{"diamond", "int"},
		{"playDays", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "platformID=" + platformID + "uuids=" + uuids + "platName=" + platName + "roleID=" + roleID + "roleSum=" + roleSum + "avatar=" + avatar + "real_name=" + real_name + "nickname=" + nickname + "usrname=" + usrname + "password=" + password + "mobile=" + mobile + "email=" + email + "birth_date=" + birth_date + "gender=" + gender + "security_question=" + security_question + "security_answer=" + security_answer + "two_factor_auth=" + two_factor_auth + "language=" + language + "timezone=" + timezone + "device=" + device + "delete_time=" + delete_time + "deleted=" + deleted + "ip=" + ip + "city=" + city + "state=" + state + "lastIP=" + lastIP + "lastLogin=" + lastLogin + "currentLogin=" + currentLogin + "userId=" + userId + "displayName=" + displayName + "familyName=" + familyName + "givenName=" + givenName + "imageUrl=" + imageUrl + "idToken=" + idToken + "serverAuthCode=" + serverAuthCode + "accessToken=" + accessToken + "money=" + money + "diamond=" + diamond + "playDays=" + playDays;
	}
	public static string[] propList = { "id","platformID","uuids","platName","roleID","roleSum","avatar","real_name","nickname","usrname","password","mobile","email","birth_date","gender","security_question","security_answer","two_factor_auth","language","timezone","device","delete_time","deleted","ip","city","state","lastIP","lastLogin","currentLogin","userId","displayName","familyName","givenName","imageUrl","idToken","serverAuthCode","accessToken","money","diamond","playDays", };
	public static Account add(Account a, Account b, uint start, uint end, Account limit = null) {
		if(a == null || b == null) return null;
		Account result = new Account();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static Account sub(Account a, Account b, uint start, uint end) {
		if(a == null || b == null) return null;
		Account result = new Account();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(Account a, Account b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static Account max(Account a, Account b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static Account json(Account a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static Account setProperty(Account a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<Account> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/Account.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("Account的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<Account> list = new List<Account>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			Account obj = new Account();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.platformID= br.ReadUInt32();
			obj.uuids= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.platName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.roleID= br.ReadInt32();
			obj.roleSum= br.ReadInt32();
			obj.avatar= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.real_name= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.nickname= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.usrname= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.password= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.mobile= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.email= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.birth_date= br.ReadInt64();
			obj.gender= br.ReadByte();
			obj.security_question= new string[1];
			int security_questionNum=br.ReadInt32();
			for (int tmp = 0; tmp < security_questionNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < security_questionNum - 1 ? "," : "");
				obj.security_question[0]+=tmpStr;
			}
			obj.security_answer= new string[1];
			int security_answerNum=br.ReadInt32();
			for (int tmp = 0; tmp < security_answerNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < security_answerNum - 1 ? "," : "");
				obj.security_answer[0]+=tmpStr;
			}
			obj.two_factor_auth= br.ReadBoolean();
			obj.language= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timezone= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.device= new string[1];
			int deviceNum=br.ReadInt32();
			for (int tmp = 0; tmp < deviceNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < deviceNum - 1 ? "," : "");
				obj.device[0]+=tmpStr;
			}
			obj.delete_time= br.ReadInt64();
			obj.deleted= br.ReadBoolean();
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.city= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.state= br.ReadByte();
			obj.lastIP= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.lastLogin= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.currentLogin= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.userId= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.displayName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.familyName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.givenName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.imageUrl= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.idToken= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverAuthCode= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.accessToken= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.money= br.ReadInt32();
			obj.diamond= br.ReadInt32();
			obj.playDays= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 Account 数据的列表</returns>
	public static List<Account> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 Account 所有数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 Account 数据列表</returns>
	public static List<Account> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.Accounts.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(Account).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 Account 类中不存在");
				return new List<Account>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(Account), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<Account, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 Account 数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 platformID 字段查询单条数据
	/// </summary>
	/// <param name="platformID">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByplatformID(uint platformID){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.platformID == platformID);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 platformID 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 platformID 字段查询多条数据
	/// </summary>
	/// <param name="platformID">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByplatformID(uint platformID){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.platformID == platformID).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 platformID 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 uuids 字段查询单条数据
	/// </summary>
	/// <param name="uuids">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByuuids(string uuids){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.uuids == uuids);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 uuids 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 uuids 字段查询多条数据
	/// </summary>
	/// <param name="uuids">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByuuids(string uuids){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.uuids == uuids).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 uuids 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 platName 字段查询单条数据
	/// </summary>
	/// <param name="platName">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByplatName(string? platName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.platName == platName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 platName 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 platName 字段查询多条数据
	/// </summary>
	/// <param name="platName">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByplatName(string? platName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.platName == platName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 platName 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 roleID 字段查询单条数据
	/// </summary>
	/// <param name="roleID">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByroleID(int roleID){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.roleID == roleID);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 roleID 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 roleID 字段查询多条数据
	/// </summary>
	/// <param name="roleID">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByroleID(int roleID){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.roleID == roleID).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 roleID 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 roleSum 字段查询单条数据
	/// </summary>
	/// <param name="roleSum">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByroleSum(int? roleSum){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.roleSum == roleSum);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 roleSum 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 roleSum 字段查询多条数据
	/// </summary>
	/// <param name="roleSum">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByroleSum(int? roleSum){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.roleSum == roleSum).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 roleSum 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 avatar 字段查询单条数据
	/// </summary>
	/// <param name="avatar">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByavatar(string? avatar){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.avatar == avatar);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 avatar 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 avatar 字段查询多条数据
	/// </summary>
	/// <param name="avatar">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByavatar(string? avatar){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.avatar == avatar).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 avatar 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 real_name 字段查询单条数据
	/// </summary>
	/// <param name="real_name">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByreal_name(string? real_name){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.real_name == real_name);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 real_name 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 real_name 字段查询多条数据
	/// </summary>
	/// <param name="real_name">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByreal_name(string? real_name){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.real_name == real_name).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 real_name 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 nickname 字段查询单条数据
	/// </summary>
	/// <param name="nickname">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBynickname(string? nickname){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.nickname == nickname);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 nickname 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 nickname 字段查询多条数据
	/// </summary>
	/// <param name="nickname">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBynickname(string? nickname){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.nickname == nickname).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 nickname 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 usrname 字段查询单条数据
	/// </summary>
	/// <param name="usrname">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByusrname(string? usrname){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.usrname == usrname);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 usrname 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 usrname 字段查询多条数据
	/// </summary>
	/// <param name="usrname">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByusrname(string? usrname){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.usrname == usrname).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 usrname 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 password 字段查询单条数据
	/// </summary>
	/// <param name="password">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBypassword(string? password){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.password == password);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 password 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 password 字段查询多条数据
	/// </summary>
	/// <param name="password">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBypassword(string? password){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.password == password).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 password 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 mobile 字段查询单条数据
	/// </summary>
	/// <param name="mobile">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBymobile(string? mobile){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.mobile == mobile);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 mobile 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 mobile 字段查询多条数据
	/// </summary>
	/// <param name="mobile">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBymobile(string? mobile){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.mobile == mobile).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 mobile 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 email 字段查询单条数据
	/// </summary>
	/// <param name="email">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByemail(string? email){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.email == email);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 email 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 email 字段查询多条数据
	/// </summary>
	/// <param name="email">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByemail(string? email){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.email == email).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 email 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 birth_date 字段查询单条数据
	/// </summary>
	/// <param name="birth_date">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBybirth_date(long? birth_date){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.birth_date == birth_date);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 birth_date 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 birth_date 字段查询多条数据
	/// </summary>
	/// <param name="birth_date">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBybirth_date(long? birth_date){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.birth_date == birth_date).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 birth_date 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 gender 字段查询单条数据
	/// </summary>
	/// <param name="gender">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBygender(byte? gender){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.gender == gender);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gender 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gender 字段查询多条数据
	/// </summary>
	/// <param name="gender">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBygender(byte? gender){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.gender == gender).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gender 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 security_question 字段查询单条数据
	/// </summary>
	/// <param name="security_question">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBysecurity_question(string[]? security_question){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.security_question == security_question);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 security_question 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 security_question 字段查询多条数据
	/// </summary>
	/// <param name="security_question">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBysecurity_question(string[]? security_question){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.security_question == security_question).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 security_question 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 security_answer 字段查询单条数据
	/// </summary>
	/// <param name="security_answer">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBysecurity_answer(string[]? security_answer){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.security_answer == security_answer);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 security_answer 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 security_answer 字段查询多条数据
	/// </summary>
	/// <param name="security_answer">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBysecurity_answer(string[]? security_answer){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.security_answer == security_answer).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 security_answer 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 two_factor_auth 字段查询单条数据
	/// </summary>
	/// <param name="two_factor_auth">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBytwo_factor_auth(bool two_factor_auth){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.two_factor_auth == two_factor_auth);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 two_factor_auth 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 two_factor_auth 字段查询多条数据
	/// </summary>
	/// <param name="two_factor_auth">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBytwo_factor_auth(bool two_factor_auth){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.two_factor_auth == two_factor_auth).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 two_factor_auth 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 language 字段查询单条数据
	/// </summary>
	/// <param name="language">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBylanguage(string? language){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.language == language);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 language 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 language 字段查询多条数据
	/// </summary>
	/// <param name="language">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBylanguage(string? language){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.language == language).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 language 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 timezone 字段查询单条数据
	/// </summary>
	/// <param name="timezone">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBytimezone(string? timezone){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.timezone == timezone);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timezone 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timezone 字段查询多条数据
	/// </summary>
	/// <param name="timezone">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBytimezone(string? timezone){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.timezone == timezone).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timezone 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 device 字段查询单条数据
	/// </summary>
	/// <param name="device">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBydevice(string[]? device){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.device == device);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 device 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 device 字段查询多条数据
	/// </summary>
	/// <param name="device">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBydevice(string[]? device){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.device == device).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 device 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 delete_time 字段查询单条数据
	/// </summary>
	/// <param name="delete_time">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBydelete_time(long? delete_time){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.delete_time == delete_time);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 delete_time 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 delete_time 字段查询多条数据
	/// </summary>
	/// <param name="delete_time">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBydelete_time(long? delete_time){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.delete_time == delete_time).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 delete_time 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 deleted 字段查询单条数据
	/// </summary>
	/// <param name="deleted">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBydeleted(bool deleted){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.deleted == deleted);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 deleted 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 deleted 字段查询多条数据
	/// </summary>
	/// <param name="deleted">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBydeleted(bool deleted){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.deleted == deleted).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 deleted 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 ip 字段查询单条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.ip == ip);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ip 字段查询多条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.ip == ip).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 city 字段查询单条数据
	/// </summary>
	/// <param name="city">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBycity(string? city){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.city == city);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 city 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 city 字段查询多条数据
	/// </summary>
	/// <param name="city">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBycity(string? city){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.city == city).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 city 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 state 字段查询单条数据
	/// </summary>
	/// <param name="state">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBystate(byte? state){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.state == state);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 state 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 state 字段查询多条数据
	/// </summary>
	/// <param name="state">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBystate(byte? state){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.state == state).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 state 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 lastIP 字段查询单条数据
	/// </summary>
	/// <param name="lastIP">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBylastIP(string? lastIP){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.lastIP == lastIP);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastIP 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 lastIP 字段查询多条数据
	/// </summary>
	/// <param name="lastIP">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBylastIP(string? lastIP){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.lastIP == lastIP).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastIP 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 lastLogin 字段查询单条数据
	/// </summary>
	/// <param name="lastLogin">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBylastLogin(string? lastLogin){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.lastLogin == lastLogin);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastLogin 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 lastLogin 字段查询多条数据
	/// </summary>
	/// <param name="lastLogin">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBylastLogin(string? lastLogin){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.lastLogin == lastLogin).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastLogin 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 currentLogin 字段查询单条数据
	/// </summary>
	/// <param name="currentLogin">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBycurrentLogin(string? currentLogin){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.currentLogin == currentLogin);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 currentLogin 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 currentLogin 字段查询多条数据
	/// </summary>
	/// <param name="currentLogin">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBycurrentLogin(string? currentLogin){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.currentLogin == currentLogin).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 currentLogin 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 userId 字段查询单条数据
	/// </summary>
	/// <param name="userId">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByuserId(string userId){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.userId == userId);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 userId 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 userId 字段查询多条数据
	/// </summary>
	/// <param name="userId">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByuserId(string userId){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.userId == userId).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 userId 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 displayName 字段查询单条数据
	/// </summary>
	/// <param name="displayName">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBydisplayName(string? displayName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.displayName == displayName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 displayName 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 displayName 字段查询多条数据
	/// </summary>
	/// <param name="displayName">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBydisplayName(string? displayName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.displayName == displayName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 displayName 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 familyName 字段查询单条数据
	/// </summary>
	/// <param name="familyName">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByfamilyName(string? familyName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.familyName == familyName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 familyName 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 familyName 字段查询多条数据
	/// </summary>
	/// <param name="familyName">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByfamilyName(string? familyName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.familyName == familyName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 familyName 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 givenName 字段查询单条数据
	/// </summary>
	/// <param name="givenName">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBygivenName(string? givenName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.givenName == givenName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 givenName 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 givenName 字段查询多条数据
	/// </summary>
	/// <param name="givenName">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBygivenName(string? givenName){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.givenName == givenName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 givenName 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 imageUrl 字段查询单条数据
	/// </summary>
	/// <param name="imageUrl">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByimageUrl(string? imageUrl){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.imageUrl == imageUrl);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 imageUrl 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 imageUrl 字段查询多条数据
	/// </summary>
	/// <param name="imageUrl">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByimageUrl(string? imageUrl){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.imageUrl == imageUrl).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 imageUrl 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 idToken 字段查询单条数据
	/// </summary>
	/// <param name="idToken">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByidToken(string idToken){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.idToken == idToken);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 idToken 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 idToken 字段查询多条数据
	/// </summary>
	/// <param name="idToken">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByidToken(string idToken){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.idToken == idToken).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 idToken 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 serverAuthCode 字段查询单条数据
	/// </summary>
	/// <param name="serverAuthCode">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByserverAuthCode(string? serverAuthCode){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.serverAuthCode == serverAuthCode);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverAuthCode 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverAuthCode 字段查询多条数据
	/// </summary>
	/// <param name="serverAuthCode">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByserverAuthCode(string? serverAuthCode){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.serverAuthCode == serverAuthCode).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverAuthCode 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 accessToken 字段查询单条数据
	/// </summary>
	/// <param name="accessToken">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByaccessToken(string? accessToken){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.accessToken == accessToken);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 accessToken 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 accessToken 字段查询多条数据
	/// </summary>
	/// <param name="accessToken">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByaccessToken(string? accessToken){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.accessToken == accessToken).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 accessToken 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 money 字段查询单条数据
	/// </summary>
	/// <param name="money">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBymoney(int? money){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.money == money);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 money 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 money 字段查询多条数据
	/// </summary>
	/// <param name="money">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBymoney(int? money){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.money == money).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 money 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 diamond 字段查询单条数据
	/// </summary>
	/// <param name="diamond">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getBydiamond(int? diamond){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.diamond == diamond);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 diamond 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 diamond 字段查询多条数据
	/// </summary>
	/// <param name="diamond">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListBydiamond(int? diamond){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.diamond == diamond).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 diamond 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}

	/// <summary>
	/// 根据 playDays 字段查询单条数据
	/// </summary>
	/// <param name="playDays">查询值</param>
	/// <returns>返回匹配的第一条 Account 数据，如果没有找到则返回 null</returns>
	public static Account getByplayDays(int? playDays){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.FirstOrDefault(x => x.playDays == playDays);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playDays 查询 Account 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 playDays 字段查询多条数据
	/// </summary>
	/// <param name="playDays">查询值</param>
	/// <returns>返回匹配的所有 Account 数据列表</returns>
	public static List<Account> getListByplayDays(int? playDays){
		try{
			var db = ORMTables.Instance;
			return db.Accounts.Where(x => x.playDays == playDays).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playDays 查询 Account 多条数据时发生错误: {ex.Message}");
			return new List<Account>();
		}
	}
}


/// <summary>
/// AI_Game_Output
/// </summary>
[Table("ai_game_outputs")]
public class AI_Game_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string? systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string? generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string? templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string? jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	/// <summary>
	/// 是否以Sse输出展示
	/// </summary>
	public bool ifSse
	{
		get;set;
	}

	/// <summary>
	/// 输出步骤
	/// </summary>
	public int? step
	{
		get;set;
	}

	/// <summary>
	/// 是否可以同组并行
	/// </summary>
	public bool ifParallel
	{
		get;set;
	}

	/// <summary>
	/// 生成优先级
	/// </summary>
	public string? priority
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/AI_Game_Output.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			systemPrompt = null;
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			generateTemplate = null;
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			templateDescription = null;
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			jsonModle = null;
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
		if(raw_data[i][7].ToString() != "")
			ifSse = (bool)Convert.ChangeType(raw_data[i][7].ToString(), typeof(bool));
		if(raw_data[i][8].ToString() != "")
			step = (int)Convert.ChangeType(raw_data[i][8].ToString(), typeof(int));
		else
			step = null;
		if(raw_data[i][9].ToString() != "")
			ifParallel = (bool)Convert.ChangeType(raw_data[i][9].ToString(), typeof(bool));
		if(raw_data[i][10].ToString() != "")
			priority = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
		else
			priority = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
		{"ifSse", "bool"},
		{"step", "int"},
		{"ifParallel", "bool"},
		{"priority", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson + "ifSse=" + ifSse + "step=" + step + "ifParallel=" + ifParallel + "priority=" + priority;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson","ifSse","step","ifParallel","priority", };
	public static AI_Game_Output add(AI_Game_Output a, AI_Game_Output b, uint start, uint end, AI_Game_Output limit = null) {
		if(a == null || b == null) return null;
		AI_Game_Output result = new AI_Game_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static AI_Game_Output sub(AI_Game_Output a, AI_Game_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		AI_Game_Output result = new AI_Game_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(AI_Game_Output a, AI_Game_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static AI_Game_Output max(AI_Game_Output a, AI_Game_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static AI_Game_Output json(AI_Game_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static AI_Game_Output setProperty(AI_Game_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<AI_Game_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/AI_Game_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("AI_Game_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<AI_Game_Output> list = new List<AI_Game_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			AI_Game_Output obj = new AI_Game_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			obj.ifSse= br.ReadBoolean();
			obj.step= br.ReadInt32();
			obj.ifParallel= br.ReadBoolean();
			obj.priority= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 AI_Game_Output 数据的列表</returns>
	public static List<AI_Game_Output> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 AI_Game_Output 所有数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.AI_Game_Outputs.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(AI_Game_Output).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 AI_Game_Output 类中不存在");
				return new List<AI_Game_Output>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(AI_Game_Output), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<AI_Game_Output, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 AI_Game_Output 数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询单条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.systemPrompt == systemPrompt);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询多条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.systemPrompt == systemPrompt).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询单条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.generateTemplate == generateTemplate);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询多条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.generateTemplate == generateTemplate).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询单条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.templateDescription == templateDescription);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询多条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.templateDescription == templateDescription).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询单条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.ifMarkdown == ifMarkdown);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询多条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.ifMarkdown == ifMarkdown).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询单条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.jsonModle == jsonModle);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询多条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.jsonModle == jsonModle).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询单条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.ifJson == ifJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询多条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.ifJson == ifJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 ifSse 字段查询单条数据
	/// </summary>
	/// <param name="ifSse">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByifSse(bool ifSse){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.ifSse == ifSse);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifSse 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifSse 字段查询多条数据
	/// </summary>
	/// <param name="ifSse">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByifSse(bool ifSse){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.ifSse == ifSse).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifSse 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 step 字段查询单条数据
	/// </summary>
	/// <param name="step">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getBystep(int? step){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.step == step);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 step 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 step 字段查询多条数据
	/// </summary>
	/// <param name="step">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListBystep(int? step){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.step == step).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 step 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 ifParallel 字段查询单条数据
	/// </summary>
	/// <param name="ifParallel">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getByifParallel(bool ifParallel){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.ifParallel == ifParallel);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifParallel 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifParallel 字段查询多条数据
	/// </summary>
	/// <param name="ifParallel">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListByifParallel(bool ifParallel){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.ifParallel == ifParallel).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifParallel 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}

	/// <summary>
	/// 根据 priority 字段查询单条数据
	/// </summary>
	/// <param name="priority">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_Output 数据，如果没有找到则返回 null</returns>
	public static AI_Game_Output getBypriority(string? priority){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.FirstOrDefault(x => x.priority == priority);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 priority 查询 AI_Game_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 priority 字段查询多条数据
	/// </summary>
	/// <param name="priority">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_Output 数据列表</returns>
	public static List<AI_Game_Output> getListBypriority(string? priority){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_Outputs.Where(x => x.priority == priority).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 priority 查询 AI_Game_Output 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_Output>();
		}
	}
}


/// <summary>
/// AI_Game_OutputData
/// </summary>
[Table("ai_game_outputdatas")]
public class AI_Game_OutputData
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 系统prompt
	/// </summary>
	public string? systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 用户prompt
	/// </summary>
	public string? userPrompt
	{
		get;set;
	}

	/// <summary>
	/// 游戏基础数据
	/// </summary>
	public string? gameBase
	{
		get;set;
	}

	/// <summary>
	/// 游戏基础数据json
	/// </summary>
	public string? gameBaseJson
	{
		get;set;
	}

	/// <summary>
	/// 世界观和游戏目标
	/// </summary>
	public string? worldViewAndTarget
	{
		get;set;
	}

	/// <summary>
	/// 世界观和游戏目标json
	/// </summary>
	public string? wolrdViewAndTargetJson
	{
		get;set;
	}

	/// <summary>
	/// ign链接json
	/// </summary>
	public string? ignJson
	{
		get;set;
	}

	/// <summary>
	/// 游戏简介
	/// </summary>
	public string? gameIntroduce
	{
		get;set;
	}

	/// <summary>
	/// 游戏简介json
	/// </summary>
	public string gameIntriduceJson
	{
		get;set;
	}

	/// <summary>
	/// 章节
	/// </summary>
	public string? chapters
	{
		get;set;
	}

	/// <summary>
	/// 章节json
	/// </summary>
	public string? chapyersJson
	{
		get;set;
	}

	/// <summary>
	/// 角色
	/// </summary>
	public string? characters
	{
		get;set;
	}

	/// <summary>
	/// 角色json
	/// </summary>
	public string? charactersJson
	{
		get;set;
	}

	/// <summary>
	/// 场景
	/// </summary>
	public string? scences
	{
		get;set;
	}

	/// <summary>
	/// 场景json
	/// </summary>
	public string? scencesJson
	{
		get;set;
	}

	/// <summary>
	/// 天空盒关键词
	/// </summary>
	public string? skyboxKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 场景关键词
	/// </summary>
	public string  scencesKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 角色关键词
	/// </summary>
	public string? charactersKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 场景音乐
	/// </summary>
	public string? scencesMusic
	{
		get;set;
	}

	/// <summary>
	/// 场景音乐关键词
	/// </summary>
	public string? scencesMusicKeys
	{
		get;set;
	}

	/// <summary>
	/// 场景音效
	/// </summary>
	public string? scencesEffect
	{
		get;set;
	}

	/// <summary>
	/// 场景音效关键词
	/// </summary>
	public string? scencesEffectKeys
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/AI_Game_OutputData.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			systemPrompt = null;
		if(raw_data[i][2].ToString() != "")
			userPrompt = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			userPrompt = null;
		if(raw_data[i][3].ToString() != "")
			gameBase = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			gameBase = null;
		if(raw_data[i][4].ToString() != "")
			gameBaseJson = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		else
			gameBaseJson = null;
		if(raw_data[i][5].ToString() != "")
			worldViewAndTarget = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			worldViewAndTarget = null;
		if(raw_data[i][6].ToString() != "")
			wolrdViewAndTargetJson = (string)Convert.ChangeType(raw_data[i][6].ToString(), typeof(string));
		else
			wolrdViewAndTargetJson = null;
		if(raw_data[i][7].ToString() != "")
			ignJson = (string)Convert.ChangeType(raw_data[i][7].ToString(), typeof(string));
		else
			ignJson = null;
		if(raw_data[i][8].ToString() != "")
			gameIntroduce = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		else
			gameIntroduce = null;
		if(raw_data[i][9].ToString() != "")
			gameIntriduceJson = (string)Convert.ChangeType(raw_data[i][9].ToString(), typeof(string));
		if(raw_data[i][10].ToString() != "")
			chapters = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
		else
			chapters = null;
		if(raw_data[i][11].ToString() != "")
			chapyersJson = (string)Convert.ChangeType(raw_data[i][11].ToString(), typeof(string));
		else
			chapyersJson = null;
		if(raw_data[i][12].ToString() != "")
			characters = (string)Convert.ChangeType(raw_data[i][12].ToString(), typeof(string));
		else
			characters = null;
		if(raw_data[i][13].ToString() != "")
			charactersJson = (string)Convert.ChangeType(raw_data[i][13].ToString(), typeof(string));
		else
			charactersJson = null;
		if(raw_data[i][14].ToString() != "")
			scences = (string)Convert.ChangeType(raw_data[i][14].ToString(), typeof(string));
		else
			scences = null;
		if(raw_data[i][15].ToString() != "")
			scencesJson = (string)Convert.ChangeType(raw_data[i][15].ToString(), typeof(string));
		else
			scencesJson = null;
		if(raw_data[i][16].ToString() != "")
			skyboxKeysJson = (string)Convert.ChangeType(raw_data[i][16].ToString(), typeof(string));
		else
			skyboxKeysJson = null;
		if(raw_data[i][17].ToString() != "")
			scencesKeysJson = (string )Convert.ChangeType(raw_data[i][17].ToString(), typeof(string ));
		if(raw_data[i][18].ToString() != "")
			charactersKeysJson = (string)Convert.ChangeType(raw_data[i][18].ToString(), typeof(string));
		else
			charactersKeysJson = null;
		if(raw_data[i][19].ToString() != "")
			scencesMusic = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
		else
			scencesMusic = null;
		if(raw_data[i][20].ToString() != "")
			scencesMusicKeys = (string)Convert.ChangeType(raw_data[i][20].ToString(), typeof(string));
		else
			scencesMusicKeys = null;
		if(raw_data[i][21].ToString() != "")
			scencesEffect = (string)Convert.ChangeType(raw_data[i][21].ToString(), typeof(string));
		else
			scencesEffect = null;
		if(raw_data[i][22].ToString() != "")
			scencesEffectKeys = (string)Convert.ChangeType(raw_data[i][22].ToString(), typeof(string));
		else
			scencesEffectKeys = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"userPrompt", "string"},
		{"gameBase", "string"},
		{"gameBaseJson", "string"},
		{"worldViewAndTarget", "string"},
		{"wolrdViewAndTargetJson", "string"},
		{"ignJson", "string"},
		{"gameIntroduce", "string"},
		{"gameIntriduceJson", "string"},
		{"chapters", "string"},
		{"chapyersJson", "string"},
		{"characters", "string"},
		{"charactersJson", "string"},
		{"scences", "string"},
		{"scencesJson", "string"},
		{"skyboxKeysJson", "string"},
		{"scencesKeysJson", "string "},
		{"charactersKeysJson", "string"},
		{"scencesMusic", "string"},
		{"scencesMusicKeys", "string"},
		{"scencesEffect", "string"},
		{"scencesEffectKeys", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "userPrompt=" + userPrompt + "gameBase=" + gameBase + "gameBaseJson=" + gameBaseJson + "worldViewAndTarget=" + worldViewAndTarget + "wolrdViewAndTargetJson=" + wolrdViewAndTargetJson + "ignJson=" + ignJson + "gameIntroduce=" + gameIntroduce + "gameIntriduceJson=" + gameIntriduceJson + "chapters=" + chapters + "chapyersJson=" + chapyersJson + "characters=" + characters + "charactersJson=" + charactersJson + "scences=" + scences + "scencesJson=" + scencesJson + "skyboxKeysJson=" + skyboxKeysJson + "scencesKeysJson=" + scencesKeysJson + "charactersKeysJson=" + charactersKeysJson + "scencesMusic=" + scencesMusic + "scencesMusicKeys=" + scencesMusicKeys + "scencesEffect=" + scencesEffect + "scencesEffectKeys=" + scencesEffectKeys;
	}
	public static string[] propList = { "id","systemPrompt","userPrompt","gameBase","gameBaseJson","worldViewAndTarget","wolrdViewAndTargetJson","ignJson","gameIntroduce","gameIntriduceJson","chapters","chapyersJson","characters","charactersJson","scences","scencesJson","skyboxKeysJson","scencesKeysJson","charactersKeysJson","scencesMusic","scencesMusicKeys","scencesEffect","scencesEffectKeys", };
	public static AI_Game_OutputData add(AI_Game_OutputData a, AI_Game_OutputData b, uint start, uint end, AI_Game_OutputData limit = null) {
		if(a == null || b == null) return null;
		AI_Game_OutputData result = new AI_Game_OutputData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static AI_Game_OutputData sub(AI_Game_OutputData a, AI_Game_OutputData b, uint start, uint end) {
		if(a == null || b == null) return null;
		AI_Game_OutputData result = new AI_Game_OutputData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(AI_Game_OutputData a, AI_Game_OutputData b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static AI_Game_OutputData max(AI_Game_OutputData a, AI_Game_OutputData b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static AI_Game_OutputData json(AI_Game_OutputData a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static AI_Game_OutputData setProperty(AI_Game_OutputData a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<AI_Game_OutputData> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/AI_Game_OutputData.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("AI_Game_OutputData的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<AI_Game_OutputData> list = new List<AI_Game_OutputData>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			AI_Game_OutputData obj = new AI_Game_OutputData();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.userPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameBase= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameBaseJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.worldViewAndTarget= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.wolrdViewAndTargetJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ignJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameIntroduce= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameIntriduceJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.chapters= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.chapyersJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.characters= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.charactersJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scences= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.skyboxKeysJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.charactersKeysJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesMusic= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesMusicKeys= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesEffect= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesEffectKeys= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 AI_Game_OutputData 数据的列表</returns>
	public static List<AI_Game_OutputData> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 AI_Game_OutputData 所有数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.AI_Game_OutputDatas.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(AI_Game_OutputData).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 AI_Game_OutputData 类中不存在");
				return new List<AI_Game_OutputData>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(AI_Game_OutputData), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<AI_Game_OutputData, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 AI_Game_OutputData 数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询单条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.systemPrompt == systemPrompt);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询多条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.systemPrompt == systemPrompt).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 userPrompt 字段查询单条数据
	/// </summary>
	/// <param name="userPrompt">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByuserPrompt(string? userPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.userPrompt == userPrompt);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 userPrompt 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 userPrompt 字段查询多条数据
	/// </summary>
	/// <param name="userPrompt">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByuserPrompt(string? userPrompt){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.userPrompt == userPrompt).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 userPrompt 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 gameBase 字段查询单条数据
	/// </summary>
	/// <param name="gameBase">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBygameBase(string? gameBase){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.gameBase == gameBase);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameBase 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gameBase 字段查询多条数据
	/// </summary>
	/// <param name="gameBase">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBygameBase(string? gameBase){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.gameBase == gameBase).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameBase 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 gameBaseJson 字段查询单条数据
	/// </summary>
	/// <param name="gameBaseJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBygameBaseJson(string? gameBaseJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.gameBaseJson == gameBaseJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameBaseJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gameBaseJson 字段查询多条数据
	/// </summary>
	/// <param name="gameBaseJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBygameBaseJson(string? gameBaseJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.gameBaseJson == gameBaseJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameBaseJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 worldViewAndTarget 字段查询单条数据
	/// </summary>
	/// <param name="worldViewAndTarget">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByworldViewAndTarget(string? worldViewAndTarget){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.worldViewAndTarget == worldViewAndTarget);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 worldViewAndTarget 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 worldViewAndTarget 字段查询多条数据
	/// </summary>
	/// <param name="worldViewAndTarget">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByworldViewAndTarget(string? worldViewAndTarget){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.worldViewAndTarget == worldViewAndTarget).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 worldViewAndTarget 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 wolrdViewAndTargetJson 字段查询单条数据
	/// </summary>
	/// <param name="wolrdViewAndTargetJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBywolrdViewAndTargetJson(string? wolrdViewAndTargetJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.wolrdViewAndTargetJson == wolrdViewAndTargetJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 wolrdViewAndTargetJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 wolrdViewAndTargetJson 字段查询多条数据
	/// </summary>
	/// <param name="wolrdViewAndTargetJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBywolrdViewAndTargetJson(string? wolrdViewAndTargetJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.wolrdViewAndTargetJson == wolrdViewAndTargetJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 wolrdViewAndTargetJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 ignJson 字段查询单条数据
	/// </summary>
	/// <param name="ignJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByignJson(string? ignJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.ignJson == ignJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ignJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ignJson 字段查询多条数据
	/// </summary>
	/// <param name="ignJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByignJson(string? ignJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.ignJson == ignJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ignJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 gameIntroduce 字段查询单条数据
	/// </summary>
	/// <param name="gameIntroduce">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBygameIntroduce(string? gameIntroduce){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.gameIntroduce == gameIntroduce);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameIntroduce 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gameIntroduce 字段查询多条数据
	/// </summary>
	/// <param name="gameIntroduce">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBygameIntroduce(string? gameIntroduce){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.gameIntroduce == gameIntroduce).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameIntroduce 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 gameIntriduceJson 字段查询单条数据
	/// </summary>
	/// <param name="gameIntriduceJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBygameIntriduceJson(string gameIntriduceJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.gameIntriduceJson == gameIntriduceJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameIntriduceJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gameIntriduceJson 字段查询多条数据
	/// </summary>
	/// <param name="gameIntriduceJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBygameIntriduceJson(string gameIntriduceJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.gameIntriduceJson == gameIntriduceJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gameIntriduceJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 chapters 字段查询单条数据
	/// </summary>
	/// <param name="chapters">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBychapters(string? chapters){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.chapters == chapters);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 chapters 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 chapters 字段查询多条数据
	/// </summary>
	/// <param name="chapters">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBychapters(string? chapters){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.chapters == chapters).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 chapters 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 chapyersJson 字段查询单条数据
	/// </summary>
	/// <param name="chapyersJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBychapyersJson(string? chapyersJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.chapyersJson == chapyersJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 chapyersJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 chapyersJson 字段查询多条数据
	/// </summary>
	/// <param name="chapyersJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBychapyersJson(string? chapyersJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.chapyersJson == chapyersJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 chapyersJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 characters 字段查询单条数据
	/// </summary>
	/// <param name="characters">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBycharacters(string? characters){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.characters == characters);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 characters 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 characters 字段查询多条数据
	/// </summary>
	/// <param name="characters">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBycharacters(string? characters){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.characters == characters).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 characters 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 charactersJson 字段查询单条数据
	/// </summary>
	/// <param name="charactersJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBycharactersJson(string? charactersJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.charactersJson == charactersJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 charactersJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 charactersJson 字段查询多条数据
	/// </summary>
	/// <param name="charactersJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBycharactersJson(string? charactersJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.charactersJson == charactersJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 charactersJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scences 字段查询单条数据
	/// </summary>
	/// <param name="scences">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscences(string? scences){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scences == scences);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scences 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scences 字段查询多条数据
	/// </summary>
	/// <param name="scences">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscences(string? scences){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scences == scences).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scences 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesJson 字段查询单条数据
	/// </summary>
	/// <param name="scencesJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesJson(string? scencesJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesJson == scencesJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesJson 字段查询多条数据
	/// </summary>
	/// <param name="scencesJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesJson(string? scencesJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesJson == scencesJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 skyboxKeysJson 字段查询单条数据
	/// </summary>
	/// <param name="skyboxKeysJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByskyboxKeysJson(string? skyboxKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.skyboxKeysJson == skyboxKeysJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 skyboxKeysJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 skyboxKeysJson 字段查询多条数据
	/// </summary>
	/// <param name="skyboxKeysJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByskyboxKeysJson(string? skyboxKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.skyboxKeysJson == skyboxKeysJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 skyboxKeysJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesKeysJson 字段查询单条数据
	/// </summary>
	/// <param name="scencesKeysJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesKeysJson(string  scencesKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesKeysJson == scencesKeysJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesKeysJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesKeysJson 字段查询多条数据
	/// </summary>
	/// <param name="scencesKeysJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesKeysJson(string  scencesKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesKeysJson == scencesKeysJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesKeysJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 charactersKeysJson 字段查询单条数据
	/// </summary>
	/// <param name="charactersKeysJson">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getBycharactersKeysJson(string? charactersKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.charactersKeysJson == charactersKeysJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 charactersKeysJson 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 charactersKeysJson 字段查询多条数据
	/// </summary>
	/// <param name="charactersKeysJson">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListBycharactersKeysJson(string? charactersKeysJson){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.charactersKeysJson == charactersKeysJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 charactersKeysJson 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesMusic 字段查询单条数据
	/// </summary>
	/// <param name="scencesMusic">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesMusic(string? scencesMusic){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesMusic == scencesMusic);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesMusic 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesMusic 字段查询多条数据
	/// </summary>
	/// <param name="scencesMusic">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesMusic(string? scencesMusic){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesMusic == scencesMusic).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesMusic 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesMusicKeys 字段查询单条数据
	/// </summary>
	/// <param name="scencesMusicKeys">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesMusicKeys(string? scencesMusicKeys){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesMusicKeys == scencesMusicKeys);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesMusicKeys 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesMusicKeys 字段查询多条数据
	/// </summary>
	/// <param name="scencesMusicKeys">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesMusicKeys(string? scencesMusicKeys){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesMusicKeys == scencesMusicKeys).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesMusicKeys 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesEffect 字段查询单条数据
	/// </summary>
	/// <param name="scencesEffect">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesEffect(string? scencesEffect){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesEffect == scencesEffect);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesEffect 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesEffect 字段查询多条数据
	/// </summary>
	/// <param name="scencesEffect">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesEffect(string? scencesEffect){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesEffect == scencesEffect).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesEffect 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}

	/// <summary>
	/// 根据 scencesEffectKeys 字段查询单条数据
	/// </summary>
	/// <param name="scencesEffectKeys">查询值</param>
	/// <returns>返回匹配的第一条 AI_Game_OutputData 数据，如果没有找到则返回 null</returns>
	public static AI_Game_OutputData getByscencesEffectKeys(string? scencesEffectKeys){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.FirstOrDefault(x => x.scencesEffectKeys == scencesEffectKeys);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesEffectKeys 查询 AI_Game_OutputData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 scencesEffectKeys 字段查询多条数据
	/// </summary>
	/// <param name="scencesEffectKeys">查询值</param>
	/// <returns>返回匹配的所有 AI_Game_OutputData 数据列表</returns>
	public static List<AI_Game_OutputData> getListByscencesEffectKeys(string? scencesEffectKeys){
		try{
			var db = ORMTables.Instance;
			return db.AI_Game_OutputDatas.Where(x => x.scencesEffectKeys == scencesEffectKeys).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 scencesEffectKeys 查询 AI_Game_OutputData 多条数据时发生错误: {ex.Message}");
			return new List<AI_Game_OutputData>();
		}
	}
}


/// <summary>
/// ArtAIChat
/// </summary>
[Table("artaichat_outputs")]
public class ArtAIChat_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string? systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string? generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string? templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string? jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/ArtAIChat.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			systemPrompt = null;
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			generateTemplate = null;
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			templateDescription = null;
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			jsonModle = null;
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson", };
	public static ArtAIChat_Output add(ArtAIChat_Output a, ArtAIChat_Output b, uint start, uint end, ArtAIChat_Output limit = null) {
		if(a == null || b == null) return null;
		ArtAIChat_Output result = new ArtAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static ArtAIChat_Output sub(ArtAIChat_Output a, ArtAIChat_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		ArtAIChat_Output result = new ArtAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(ArtAIChat_Output a, ArtAIChat_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static ArtAIChat_Output max(ArtAIChat_Output a, ArtAIChat_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static ArtAIChat_Output json(ArtAIChat_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static ArtAIChat_Output setProperty(ArtAIChat_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<ArtAIChat_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/ArtAIChat_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("ArtAIChat_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<ArtAIChat_Output> list = new List<ArtAIChat_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			ArtAIChat_Output obj = new ArtAIChat_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 ArtAIChat_Output 数据的列表</returns>
	public static List<ArtAIChat_Output> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 ArtAIChat_Output 所有数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.ArtAIChat_Outputs.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(ArtAIChat_Output).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 ArtAIChat_Output 类中不存在");
				return new List<ArtAIChat_Output>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(ArtAIChat_Output), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<ArtAIChat_Output, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 ArtAIChat_Output 数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询单条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.systemPrompt == systemPrompt);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询多条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.systemPrompt == systemPrompt).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询单条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.generateTemplate == generateTemplate);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询多条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.generateTemplate == generateTemplate).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询单条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.templateDescription == templateDescription);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询多条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.templateDescription == templateDescription).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询单条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.ifMarkdown == ifMarkdown);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询多条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.ifMarkdown == ifMarkdown).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询单条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.jsonModle == jsonModle);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询多条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.jsonModle == jsonModle).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询单条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的第一条 ArtAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static ArtAIChat_Output getByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.FirstOrDefault(x => x.ifJson == ifJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 ArtAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询多条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的所有 ArtAIChat_Output 数据列表</returns>
	public static List<ArtAIChat_Output> getListByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.ArtAIChat_Outputs.Where(x => x.ifJson == ifJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 ArtAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<ArtAIChat_Output>();
		}
	}
}


/// <summary>
/// DesignAIChat
/// </summary>
[Table("designaichat_outputs")]
public class DesignAIChat_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string? systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string? generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string? templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string? jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/DesignAIChat.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			systemPrompt = null;
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			generateTemplate = null;
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			templateDescription = null;
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			jsonModle = null;
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson", };
	public static DesignAIChat_Output add(DesignAIChat_Output a, DesignAIChat_Output b, uint start, uint end, DesignAIChat_Output limit = null) {
		if(a == null || b == null) return null;
		DesignAIChat_Output result = new DesignAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static DesignAIChat_Output sub(DesignAIChat_Output a, DesignAIChat_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		DesignAIChat_Output result = new DesignAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(DesignAIChat_Output a, DesignAIChat_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static DesignAIChat_Output max(DesignAIChat_Output a, DesignAIChat_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static DesignAIChat_Output json(DesignAIChat_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static DesignAIChat_Output setProperty(DesignAIChat_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<DesignAIChat_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/DesignAIChat_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("DesignAIChat_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<DesignAIChat_Output> list = new List<DesignAIChat_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			DesignAIChat_Output obj = new DesignAIChat_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 DesignAIChat_Output 数据的列表</returns>
	public static List<DesignAIChat_Output> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 DesignAIChat_Output 所有数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.DesignAIChat_Outputs.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(DesignAIChat_Output).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 DesignAIChat_Output 类中不存在");
				return new List<DesignAIChat_Output>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(DesignAIChat_Output), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<DesignAIChat_Output, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 DesignAIChat_Output 数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询单条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.systemPrompt == systemPrompt);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 systemPrompt 字段查询多条数据
	/// </summary>
	/// <param name="systemPrompt">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListBysystemPrompt(string? systemPrompt){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.systemPrompt == systemPrompt).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 systemPrompt 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询单条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.generateTemplate == generateTemplate);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 generateTemplate 字段查询多条数据
	/// </summary>
	/// <param name="generateTemplate">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListBygenerateTemplate(string? generateTemplate){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.generateTemplate == generateTemplate).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 generateTemplate 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询单条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.templateDescription == templateDescription);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 templateDescription 字段查询多条数据
	/// </summary>
	/// <param name="templateDescription">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListBytemplateDescription(string? templateDescription){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.templateDescription == templateDescription).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 templateDescription 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询单条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.ifMarkdown == ifMarkdown);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifMarkdown 字段查询多条数据
	/// </summary>
	/// <param name="ifMarkdown">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListByifMarkdown(bool ifMarkdown){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.ifMarkdown == ifMarkdown).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifMarkdown 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询单条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.jsonModle == jsonModle);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 jsonModle 字段查询多条数据
	/// </summary>
	/// <param name="jsonModle">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListByjsonModle(string? jsonModle){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.jsonModle == jsonModle).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 jsonModle 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询单条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的第一条 DesignAIChat_Output 数据，如果没有找到则返回 null</returns>
	public static DesignAIChat_Output getByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.FirstOrDefault(x => x.ifJson == ifJson);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 DesignAIChat_Output 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ifJson 字段查询多条数据
	/// </summary>
	/// <param name="ifJson">查询值</param>
	/// <returns>返回匹配的所有 DesignAIChat_Output 数据列表</returns>
	public static List<DesignAIChat_Output> getListByifJson(bool ifJson){
		try{
			var db = ORMTables.Instance;
			return db.DesignAIChat_Outputs.Where(x => x.ifJson == ifJson).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ifJson 查询 DesignAIChat_Output 多条数据时发生错误: {ex.Message}");
			return new List<DesignAIChat_Output>();
		}
	}
}


/// <summary>
/// ErrorInfo
/// </summary>
[Table("errorinfos")]
public class ErrorInfo
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 错误消息
	/// </summary>
	public string? message
	{
		get;set;
	}

	/// <summary>
	/// 异常类型
	/// </summary>
	public string? errorType
	{
		get;set;
	}

	/// <summary>
	/// 创建时间
	/// </summary>
	public string? time
	{
		get;set;
	}

	/// <summary>
	/// 机型
	/// </summary>
	public string? modelType
	{
		get;set;
	}

	/// <summary>
	/// IP地址
	/// </summary>
	public string? ip
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/ErrorInfo.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			message = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			message = null;
		if(raw_data[i][2].ToString() != "")
			errorType = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			errorType = null;
		if(raw_data[i][3].ToString() != "")
			time = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			time = null;
		if(raw_data[i][4].ToString() != "")
			modelType = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		else
			modelType = null;
		if(raw_data[i][5].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			ip = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"message", "string"},
		{"errorType", "string"},
		{"time", "string"},
		{"modelType", "string"},
		{"ip", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "message=" + message + "errorType=" + errorType + "time=" + time + "modelType=" + modelType + "ip=" + ip;
	}
	public static string[] propList = { "id","message","errorType","time","modelType","ip", };
	public static ErrorInfo add(ErrorInfo a, ErrorInfo b, uint start, uint end, ErrorInfo limit = null) {
		if(a == null || b == null) return null;
		ErrorInfo result = new ErrorInfo();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static ErrorInfo sub(ErrorInfo a, ErrorInfo b, uint start, uint end) {
		if(a == null || b == null) return null;
		ErrorInfo result = new ErrorInfo();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(ErrorInfo a, ErrorInfo b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static ErrorInfo max(ErrorInfo a, ErrorInfo b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static ErrorInfo json(ErrorInfo a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static ErrorInfo setProperty(ErrorInfo a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<ErrorInfo> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/ErrorInfo.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("ErrorInfo的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<ErrorInfo> list = new List<ErrorInfo>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			ErrorInfo obj = new ErrorInfo();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.message= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.errorType= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.time= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.modelType= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 ErrorInfo 数据的列表</returns>
	public static List<ErrorInfo> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 ErrorInfo 所有数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.ErrorInfos.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(ErrorInfo).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 ErrorInfo 类中不存在");
				return new List<ErrorInfo>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(ErrorInfo), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<ErrorInfo, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 ErrorInfo 数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 message 字段查询单条数据
	/// </summary>
	/// <param name="message">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getBymessage(string? message){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.message == message);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 message 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 message 字段查询多条数据
	/// </summary>
	/// <param name="message">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListBymessage(string? message){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.message == message).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 message 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 errorType 字段查询单条数据
	/// </summary>
	/// <param name="errorType">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getByerrorType(string? errorType){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.errorType == errorType);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 errorType 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 errorType 字段查询多条数据
	/// </summary>
	/// <param name="errorType">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListByerrorType(string? errorType){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.errorType == errorType).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 errorType 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 time 字段查询单条数据
	/// </summary>
	/// <param name="time">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getBytime(string? time){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.time == time);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 time 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 time 字段查询多条数据
	/// </summary>
	/// <param name="time">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListBytime(string? time){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.time == time).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 time 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 modelType 字段查询单条数据
	/// </summary>
	/// <param name="modelType">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getBymodelType(string? modelType){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.modelType == modelType);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 modelType 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 modelType 字段查询多条数据
	/// </summary>
	/// <param name="modelType">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListBymodelType(string? modelType){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.modelType == modelType).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 modelType 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}

	/// <summary>
	/// 根据 ip 字段查询单条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的第一条 ErrorInfo 数据，如果没有找到则返回 null</returns>
	public static ErrorInfo getByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.FirstOrDefault(x => x.ip == ip);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 ErrorInfo 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ip 字段查询多条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的所有 ErrorInfo 数据列表</returns>
	public static List<ErrorInfo> getListByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.ErrorInfos.Where(x => x.ip == ip).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 ErrorInfo 多条数据时发生错误: {ex.Message}");
			return new List<ErrorInfo>();
		}
	}
}


/// <summary>
/// serverVar
/// </summary>
[Table("servervars")]
public class serverVar
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 变量名
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 变量类型
	/// </summary>
	public string? type
	{
		get;set;
	}

	/// <summary>
	/// 变量值
	/// </summary>
	public string? value
	{
		get;set;
	}

	/// <summary>
	/// 保存时间类型
	/// </summary>
	public byte? timeType
	{
		get;set;
	}

	/// <summary>
	/// 时间值
	/// </summary>
	public int? timeValue
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/serverVar.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			type = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			type = null;
		if(raw_data[i][2].ToString() != "")
			value = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			value = null;
		if(raw_data[i][3].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][3].ToString(), typeof(byte));
		else
			timeType = null;
		if(raw_data[i][4].ToString() != "")
			timeValue = (int)Convert.ChangeType(raw_data[i][4].ToString(), typeof(int));
		else
			timeValue = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"type", "string"},
		{"value", "string"},
		{"timeType", "byte"},
		{"timeValue", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "type=" + type + "value=" + value + "timeType=" + timeType + "timeValue=" + timeValue;
	}
	public static string[] propList = { "id","type","value","timeType","timeValue", };
	public static serverVar add(serverVar a, serverVar b, uint start, uint end, serverVar limit = null) {
		if(a == null || b == null) return null;
		serverVar result = new serverVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static serverVar sub(serverVar a, serverVar b, uint start, uint end) {
		if(a == null || b == null) return null;
		serverVar result = new serverVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(serverVar a, serverVar b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static serverVar max(serverVar a, serverVar b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static serverVar json(serverVar a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static serverVar setProperty(serverVar a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<serverVar> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/serverVar.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("serverVar的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<serverVar> list = new List<serverVar>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			serverVar obj = new serverVar();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.type= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.value= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timeType= br.ReadByte();
			obj.timeValue= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 serverVar 数据的列表</returns>
	public static List<serverVar> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 serverVar 所有数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 serverVar 数据列表</returns>
	public static List<serverVar> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.serverVars.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(serverVar).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 serverVar 类中不存在");
				return new List<serverVar>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(serverVar), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<serverVar, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 serverVar 数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 serverVar 数据，如果没有找到则返回 null</returns>
	public static serverVar getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 serverVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 serverVar 数据列表</returns>
	public static List<serverVar> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 serverVar 多条数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据 type 字段查询单条数据
	/// </summary>
	/// <param name="type">查询值</param>
	/// <returns>返回匹配的第一条 serverVar 数据，如果没有找到则返回 null</returns>
	public static serverVar getBytype(string? type){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.FirstOrDefault(x => x.type == type);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 type 查询 serverVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 type 字段查询多条数据
	/// </summary>
	/// <param name="type">查询值</param>
	/// <returns>返回匹配的所有 serverVar 数据列表</returns>
	public static List<serverVar> getListBytype(string? type){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.Where(x => x.type == type).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 type 查询 serverVar 多条数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据 value 字段查询单条数据
	/// </summary>
	/// <param name="value">查询值</param>
	/// <returns>返回匹配的第一条 serverVar 数据，如果没有找到则返回 null</returns>
	public static serverVar getByvalue(string? value){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.FirstOrDefault(x => x.value == value);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 value 查询 serverVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 value 字段查询多条数据
	/// </summary>
	/// <param name="value">查询值</param>
	/// <returns>返回匹配的所有 serverVar 数据列表</returns>
	public static List<serverVar> getListByvalue(string? value){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.Where(x => x.value == value).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 value 查询 serverVar 多条数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询单条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的第一条 serverVar 数据，如果没有找到则返回 null</returns>
	public static serverVar getBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.FirstOrDefault(x => x.timeType == timeType);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 serverVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询多条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的所有 serverVar 数据列表</returns>
	public static List<serverVar> getListBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.Where(x => x.timeType == timeType).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 serverVar 多条数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}

	/// <summary>
	/// 根据 timeValue 字段查询单条数据
	/// </summary>
	/// <param name="timeValue">查询值</param>
	/// <returns>返回匹配的第一条 serverVar 数据，如果没有找到则返回 null</returns>
	public static serverVar getBytimeValue(int? timeValue){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.FirstOrDefault(x => x.timeValue == timeValue);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeValue 查询 serverVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timeValue 字段查询多条数据
	/// </summary>
	/// <param name="timeValue">查询值</param>
	/// <returns>返回匹配的所有 serverVar 数据列表</returns>
	public static List<serverVar> getListBytimeValue(int? timeValue){
		try{
			var db = ORMTables.Instance;
			return db.serverVars.Where(x => x.timeValue == timeValue).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeValue 查询 serverVar 多条数据时发生错误: {ex.Message}");
			return new List<serverVar>();
		}
	}
}


/// <summary>
/// SeverConfig
/// </summary>
[Table("severconfigbases")]
public class SeverConfigBase
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 备注
	/// </summary>
	public string? depict
	{
		get;set;
	}

	/// <summary>
	/// ip
	/// </summary>
	public string? ip
	{
		get;set;
	}

	/// <summary>
	/// 网关地址
	/// </summary>
	public string? gateWay
	{
		get;set;
	}

	/// <summary>
	/// 区服ID
	/// </summary>
	public string serverID
	{
		get;set;
	}

	/// <summary>
	/// 区服名称
	/// </summary>
	public string? serverName
	{
		get;set;
	}

	/// <summary>
	/// 	/// 区服状态:
	/// 1.爆满
	/// 2.维护
	/// 3.流畅

	/// </summary>
	public byte? serverState
	{
		get;set;
	}

	/// <summary>
	/// 新区
	/// </summary>
	public bool newServer
	{
		get;set;
	}

	/// <summary>
	/// MAC地址
	/// </summary>
	public string? MAC
	{
		get;set;
	}

	/// <summary>
	/// 启动时间
	/// </summary>
	public long? setupTime
	{
		get;set;
	}

	/// <summary>
	/// 状态
	/// </summary>
	public byte? status
	{
		get;set;
	}

	/// <summary>
	/// 当前人数
	/// </summary>
	public uint? playerSum
	{
		get;set;
	}

	/// <summary>
	/// 是否为网关
	/// </summary>
	public bool isGate
	{
		get;set;
	}

	/// <summary>
	/// 备注IP
	/// </summary>
	public string? descIP
	{
		get;set;
	}

	/// <summary>
	/// 地图更新序号
	/// </summary>
	public ulong? mapSaveVer
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SeverConfig.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			depict = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			depict = null;
		if(raw_data[i][2].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			ip = null;
		if(raw_data[i][3].ToString() != "")
			gateWay = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			gateWay = null;
		if(raw_data[i][4].ToString() != "")
			serverID = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		if(raw_data[i][5].ToString() != "")
			serverName = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		else
			serverName = null;
		if(raw_data[i][6].ToString() != "")
			serverState = (byte)Convert.ChangeType(raw_data[i][6].ToString(), typeof(byte));
		else
			serverState = null;
		if(raw_data[i][7].ToString() != "")
			newServer = (bool)Convert.ChangeType(raw_data[i][7].ToString(), typeof(bool));
		if(raw_data[i][8].ToString() != "")
			MAC = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		else
			MAC = null;
		if(raw_data[i][9].ToString() != "")
			setupTime = (long)Convert.ChangeType(raw_data[i][9].ToString(), typeof(long));
		else
			setupTime = null;
		if(raw_data[i][10].ToString() != "")
			status = (byte)Convert.ChangeType(raw_data[i][10].ToString(), typeof(byte));
		else
			status = null;
		if(raw_data[i][11].ToString() != "")
			playerSum = (uint)Convert.ChangeType(raw_data[i][11].ToString(), typeof(uint));
		else
			playerSum = null;
		if(raw_data[i][12].ToString() != "")
			isGate = (bool)Convert.ChangeType(raw_data[i][12].ToString(), typeof(bool));
		if(raw_data[i][13].ToString() != "")
			descIP = (string)Convert.ChangeType(raw_data[i][13].ToString(), typeof(string));
		else
			descIP = null;
		if(raw_data[i][14].ToString() != "")
			mapSaveVer = (ulong)Convert.ChangeType(raw_data[i][14].ToString(), typeof(ulong));
		else
			mapSaveVer = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"depict", "string"},
		{"ip", "string"},
		{"gateWay", "string"},
		{"serverID", "string"},
		{"serverName", "string"},
		{"serverState", "byte"},
		{"newServer", "bool"},
		{"MAC", "string"},
		{"setupTime", "long"},
		{"status", "byte"},
		{"playerSum", "uint"},
		{"isGate", "bool"},
		{"descIP", "string"},
		{"mapSaveVer", "ulong"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "depict=" + depict + "ip=" + ip + "gateWay=" + gateWay + "serverID=" + serverID + "serverName=" + serverName + "serverState=" + serverState + "newServer=" + newServer + "MAC=" + MAC + "setupTime=" + setupTime + "status=" + status + "playerSum=" + playerSum + "isGate=" + isGate + "descIP=" + descIP + "mapSaveVer=" + mapSaveVer;
	}
	public static string[] propList = { "id","depict","ip","gateWay","serverID","serverName","serverState","newServer","MAC","setupTime","status","playerSum","isGate","descIP","mapSaveVer", };
	public static SeverConfigBase add(SeverConfigBase a, SeverConfigBase b, uint start, uint end, SeverConfigBase limit = null) {
		if(a == null || b == null) return null;
		SeverConfigBase result = new SeverConfigBase();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SeverConfigBase sub(SeverConfigBase a, SeverConfigBase b, uint start, uint end) {
		if(a == null || b == null) return null;
		SeverConfigBase result = new SeverConfigBase();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SeverConfigBase a, SeverConfigBase b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SeverConfigBase max(SeverConfigBase a, SeverConfigBase b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SeverConfigBase json(SeverConfigBase a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SeverConfigBase setProperty(SeverConfigBase a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SeverConfigBase> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SeverConfigBase.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SeverConfigBase的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SeverConfigBase> list = new List<SeverConfigBase>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SeverConfigBase obj = new SeverConfigBase();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.depict= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gateWay= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverID= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverState= br.ReadByte();
			obj.newServer= br.ReadBoolean();
			obj.MAC= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.setupTime= br.ReadInt64();
			obj.status= br.ReadByte();
			obj.playerSum= br.ReadUInt32();
			obj.isGate= br.ReadBoolean();
			obj.descIP= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.mapSaveVer= br.ReadUInt64();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 SeverConfigBase 数据的列表</returns>
	public static List<SeverConfigBase> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 SeverConfigBase 所有数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.SeverConfigBases.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(SeverConfigBase).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 SeverConfigBase 类中不存在");
				return new List<SeverConfigBase>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(SeverConfigBase), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<SeverConfigBase, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 SeverConfigBase 数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 depict 字段查询单条数据
	/// </summary>
	/// <param name="depict">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBydepict(string? depict){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.depict == depict);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 depict 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 depict 字段查询多条数据
	/// </summary>
	/// <param name="depict">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBydepict(string? depict){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.depict == depict).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 depict 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 ip 字段查询单条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.ip == ip);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 ip 字段查询多条数据
	/// </summary>
	/// <param name="ip">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByip(string? ip){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.ip == ip).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 ip 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 gateWay 字段查询单条数据
	/// </summary>
	/// <param name="gateWay">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBygateWay(string? gateWay){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.gateWay == gateWay);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gateWay 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 gateWay 字段查询多条数据
	/// </summary>
	/// <param name="gateWay">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBygateWay(string? gateWay){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.gateWay == gateWay).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 gateWay 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 serverID 字段查询单条数据
	/// </summary>
	/// <param name="serverID">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByserverID(string serverID){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.serverID == serverID);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverID 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverID 字段查询多条数据
	/// </summary>
	/// <param name="serverID">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByserverID(string serverID){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.serverID == serverID).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverID 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 serverName 字段查询单条数据
	/// </summary>
	/// <param name="serverName">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByserverName(string? serverName){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.serverName == serverName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverName 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverName 字段查询多条数据
	/// </summary>
	/// <param name="serverName">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByserverName(string? serverName){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.serverName == serverName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverName 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 serverState 字段查询单条数据
	/// </summary>
	/// <param name="serverState">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByserverState(byte? serverState){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.serverState == serverState);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverState 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverState 字段查询多条数据
	/// </summary>
	/// <param name="serverState">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByserverState(byte? serverState){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.serverState == serverState).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverState 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 newServer 字段查询单条数据
	/// </summary>
	/// <param name="newServer">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBynewServer(bool newServer){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.newServer == newServer);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 newServer 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 newServer 字段查询多条数据
	/// </summary>
	/// <param name="newServer">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBynewServer(bool newServer){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.newServer == newServer).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 newServer 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 MAC 字段查询单条数据
	/// </summary>
	/// <param name="MAC">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByMAC(string? MAC){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.MAC == MAC);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 MAC 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 MAC 字段查询多条数据
	/// </summary>
	/// <param name="MAC">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByMAC(string? MAC){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.MAC == MAC).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 MAC 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 setupTime 字段查询单条数据
	/// </summary>
	/// <param name="setupTime">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBysetupTime(long? setupTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.setupTime == setupTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 setupTime 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 setupTime 字段查询多条数据
	/// </summary>
	/// <param name="setupTime">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBysetupTime(long? setupTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.setupTime == setupTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 setupTime 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 status 字段查询单条数据
	/// </summary>
	/// <param name="status">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBystatus(byte? status){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.status == status);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 status 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 status 字段查询多条数据
	/// </summary>
	/// <param name="status">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBystatus(byte? status){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.status == status).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 status 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 playerSum 字段查询单条数据
	/// </summary>
	/// <param name="playerSum">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByplayerSum(uint? playerSum){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.playerSum == playerSum);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playerSum 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 playerSum 字段查询多条数据
	/// </summary>
	/// <param name="playerSum">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByplayerSum(uint? playerSum){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.playerSum == playerSum).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playerSum 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 isGate 字段查询单条数据
	/// </summary>
	/// <param name="isGate">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getByisGate(bool isGate){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.isGate == isGate);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 isGate 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 isGate 字段查询多条数据
	/// </summary>
	/// <param name="isGate">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListByisGate(bool isGate){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.isGate == isGate).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 isGate 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 descIP 字段查询单条数据
	/// </summary>
	/// <param name="descIP">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBydescIP(string? descIP){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.descIP == descIP);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 descIP 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 descIP 字段查询多条数据
	/// </summary>
	/// <param name="descIP">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBydescIP(string? descIP){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.descIP == descIP).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 descIP 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}

	/// <summary>
	/// 根据 mapSaveVer 字段查询单条数据
	/// </summary>
	/// <param name="mapSaveVer">查询值</param>
	/// <returns>返回匹配的第一条 SeverConfigBase 数据，如果没有找到则返回 null</returns>
	public static SeverConfigBase getBymapSaveVer(ulong? mapSaveVer){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.FirstOrDefault(x => x.mapSaveVer == mapSaveVer);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 mapSaveVer 查询 SeverConfigBase 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 mapSaveVer 字段查询多条数据
	/// </summary>
	/// <param name="mapSaveVer">查询值</param>
	/// <returns>返回匹配的所有 SeverConfigBase 数据列表</returns>
	public static List<SeverConfigBase> getListBymapSaveVer(ulong? mapSaveVer){
		try{
			var db = ORMTables.Instance;
			return db.SeverConfigBases.Where(x => x.mapSaveVer == mapSaveVer).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 mapSaveVer 查询 SeverConfigBase 多条数据时发生错误: {ex.Message}");
			return new List<SeverConfigBase>();
		}
	}
}


/// <summary>
/// SeverData
/// </summary>
[Table("severdatas")]
public class SeverData
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 	/// 区服状态:
	/// 1.爆满
	/// 2.维护
	/// 3.流畅

	/// </summary>
	public byte? serverState
	{
		get;set;
	}

	/// <summary>
	/// 新区
	/// </summary>
	public bool newServer
	{
		get;set;
	}

	/// <summary>
	/// 启动时间
	/// </summary>
	public long? setupTime
	{
		get;set;
	}

	/// <summary>
	/// 状态
	/// </summary>
	public byte? status
	{
		get;set;
	}

	/// <summary>
	/// 当前人数
	/// </summary>
	public uint? playerSum
	{
		get;set;
	}

	/// <summary>
	/// 开服时间
	/// </summary>
	public long? openTime
	{
		get;set;
	}

	/// <summary>
	/// 服务器偏移时间
	/// </summary>
	public long? addTime
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SeverData.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			serverState = (byte)Convert.ChangeType(raw_data[i][1].ToString(), typeof(byte));
		else
			serverState = null;
		if(raw_data[i][2].ToString() != "")
			newServer = (bool)Convert.ChangeType(raw_data[i][2].ToString(), typeof(bool));
		if(raw_data[i][3].ToString() != "")
			setupTime = (long)Convert.ChangeType(raw_data[i][3].ToString(), typeof(long));
		else
			setupTime = null;
		if(raw_data[i][4].ToString() != "")
			status = (byte)Convert.ChangeType(raw_data[i][4].ToString(), typeof(byte));
		else
			status = null;
		if(raw_data[i][5].ToString() != "")
			playerSum = (uint)Convert.ChangeType(raw_data[i][5].ToString(), typeof(uint));
		else
			playerSum = null;
		if(raw_data[i][6].ToString() != "")
			openTime = (long)Convert.ChangeType(raw_data[i][6].ToString(), typeof(long));
		else
			openTime = null;
		if(raw_data[i][7].ToString() != "")
			addTime = (long)Convert.ChangeType(raw_data[i][7].ToString(), typeof(long));
		else
			addTime = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"serverState", "byte"},
		{"newServer", "bool"},
		{"setupTime", "long"},
		{"status", "byte"},
		{"playerSum", "uint"},
		{"openTime", "long"},
		{"addTime", "long"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "serverState=" + serverState + "newServer=" + newServer + "setupTime=" + setupTime + "status=" + status + "playerSum=" + playerSum + "openTime=" + openTime + "addTime=" + addTime;
	}
	public static string[] propList = { "id","serverState","newServer","setupTime","status","playerSum","openTime","addTime", };
	public static SeverData add(SeverData a, SeverData b, uint start, uint end, SeverData limit = null) {
		if(a == null || b == null) return null;
		SeverData result = new SeverData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SeverData sub(SeverData a, SeverData b, uint start, uint end) {
		if(a == null || b == null) return null;
		SeverData result = new SeverData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SeverData a, SeverData b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SeverData max(SeverData a, SeverData b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SeverData json(SeverData a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SeverData setProperty(SeverData a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SeverData> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SeverData.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SeverData的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SeverData> list = new List<SeverData>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SeverData obj = new SeverData();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverState= br.ReadByte();
			obj.newServer= br.ReadBoolean();
			obj.setupTime= br.ReadInt64();
			obj.status= br.ReadByte();
			obj.playerSum= br.ReadUInt32();
			obj.openTime= br.ReadInt64();
			obj.addTime= br.ReadInt64();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 SeverData 数据的列表</returns>
	public static List<SeverData> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 SeverData 所有数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 SeverData 数据列表</returns>
	public static List<SeverData> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.SeverDatas.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(SeverData).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 SeverData 类中不存在");
				return new List<SeverData>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(SeverData), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<SeverData, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 SeverData 数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 serverState 字段查询单条数据
	/// </summary>
	/// <param name="serverState">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getByserverState(byte? serverState){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.serverState == serverState);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverState 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverState 字段查询多条数据
	/// </summary>
	/// <param name="serverState">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListByserverState(byte? serverState){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.serverState == serverState).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverState 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 newServer 字段查询单条数据
	/// </summary>
	/// <param name="newServer">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getBynewServer(bool newServer){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.newServer == newServer);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 newServer 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 newServer 字段查询多条数据
	/// </summary>
	/// <param name="newServer">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListBynewServer(bool newServer){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.newServer == newServer).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 newServer 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 setupTime 字段查询单条数据
	/// </summary>
	/// <param name="setupTime">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getBysetupTime(long? setupTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.setupTime == setupTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 setupTime 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 setupTime 字段查询多条数据
	/// </summary>
	/// <param name="setupTime">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListBysetupTime(long? setupTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.setupTime == setupTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 setupTime 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 status 字段查询单条数据
	/// </summary>
	/// <param name="status">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getBystatus(byte? status){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.status == status);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 status 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 status 字段查询多条数据
	/// </summary>
	/// <param name="status">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListBystatus(byte? status){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.status == status).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 status 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 playerSum 字段查询单条数据
	/// </summary>
	/// <param name="playerSum">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getByplayerSum(uint? playerSum){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.playerSum == playerSum);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playerSum 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 playerSum 字段查询多条数据
	/// </summary>
	/// <param name="playerSum">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListByplayerSum(uint? playerSum){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.playerSum == playerSum).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 playerSum 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 openTime 字段查询单条数据
	/// </summary>
	/// <param name="openTime">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getByopenTime(long? openTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.openTime == openTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 openTime 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 openTime 字段查询多条数据
	/// </summary>
	/// <param name="openTime">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListByopenTime(long? openTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.openTime == openTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 openTime 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}

	/// <summary>
	/// 根据 addTime 字段查询单条数据
	/// </summary>
	/// <param name="addTime">查询值</param>
	/// <returns>返回匹配的第一条 SeverData 数据，如果没有找到则返回 null</returns>
	public static SeverData getByaddTime(long? addTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.FirstOrDefault(x => x.addTime == addTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 addTime 查询 SeverData 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 addTime 字段查询多条数据
	/// </summary>
	/// <param name="addTime">查询值</param>
	/// <returns>返回匹配的所有 SeverData 数据列表</returns>
	public static List<SeverData> getListByaddTime(long? addTime){
		try{
			var db = ORMTables.Instance;
			return db.SeverDatas.Where(x => x.addTime == addTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 addTime 查询 SeverData 多条数据时发生错误: {ex.Message}");
			return new List<SeverData>();
		}
	}
}


/// <summary>
/// SysActive
/// </summary>
[Table("sysactives")]
public class SysActive
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 系统id
	/// </summary>
	public uint id
	{
		get;set;
	}

	/// <summary>
	/// 系统中文名字
	/// </summary>
	public string? sysName_zh
	{
		get;set;
	}

	/// <summary>
	/// 系统英文名字
	/// </summary>
	public string? sysName_en
	{
		get;set;
	}

	/// <summary>
	/// 激活方式
	/// </summary>
	public uint? active
	{
		get;set;
	}

	/// <summary>
	/// 激活状态
	/// </summary>
	public uint? activityStatus
	{
		get;set;
	}

	/// <summary>
	/// 加载方式
	/// </summary>
	public uint? loadingMethod
	{
		get;set;
	}

	/// <summary>
	/// 启动数据统计
	/// </summary>
	public uint? startDataTracing
	{
		get;set;
	}

	/// <summary>
	/// 系统模块
	/// </summary>
	public uint? sysModule
	{
		get;set;
	}

	/// <summary>
	/// 模块版本号
	/// </summary>
	public uint? moduleVersion
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SysActive.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (uint)Convert.ChangeType(raw_data[i][0].ToString(), typeof(uint));
		if(raw_data[i][1].ToString() != "")
			sysName_zh = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			sysName_zh = null;
		if(raw_data[i][2].ToString() != "")
			sysName_en = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			sysName_en = null;
		if(raw_data[i][3].ToString() != "")
			active = (uint)Convert.ChangeType(raw_data[i][3].ToString(), typeof(uint));
		else
			active = null;
		if(raw_data[i][4].ToString() != "")
			activityStatus = (uint)Convert.ChangeType(raw_data[i][4].ToString(), typeof(uint));
		else
			activityStatus = null;
		if(raw_data[i][5].ToString() != "")
			loadingMethod = (uint)Convert.ChangeType(raw_data[i][5].ToString(), typeof(uint));
		else
			loadingMethod = null;
		if(raw_data[i][6].ToString() != "")
			startDataTracing = (uint)Convert.ChangeType(raw_data[i][6].ToString(), typeof(uint));
		else
			startDataTracing = null;
		if(raw_data[i][7].ToString() != "")
			sysModule = (uint)Convert.ChangeType(raw_data[i][7].ToString(), typeof(uint));
		else
			sysModule = null;
		if(raw_data[i][8].ToString() != "")
			moduleVersion = (uint)Convert.ChangeType(raw_data[i][8].ToString(), typeof(uint));
		else
			moduleVersion = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "uint"},
		{"sysName_zh", "string"},
		{"sysName_en", "string"},
		{"active", "uint"},
		{"activityStatus", "uint"},
		{"loadingMethod", "uint"},
		{"startDataTracing", "uint"},
		{"sysModule", "uint"},
		{"moduleVersion", "uint"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "sysName_zh=" + sysName_zh + "sysName_en=" + sysName_en + "active=" + active + "activityStatus=" + activityStatus + "loadingMethod=" + loadingMethod + "startDataTracing=" + startDataTracing + "sysModule=" + sysModule + "moduleVersion=" + moduleVersion;
	}
	public static string[] propList = { "id","sysName_zh","sysName_en","active","activityStatus","loadingMethod","startDataTracing","sysModule","moduleVersion", };
	public static SysActive add(SysActive a, SysActive b, uint start, uint end, SysActive limit = null) {
		if(a == null || b == null) return null;
		SysActive result = new SysActive();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SysActive sub(SysActive a, SysActive b, uint start, uint end) {
		if(a == null || b == null) return null;
		SysActive result = new SysActive();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SysActive a, SysActive b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SysActive max(SysActive a, SysActive b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SysActive json(SysActive a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SysActive setProperty(SysActive a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SysActive> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SysActive.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SysActive的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SysActive> list = new List<SysActive>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SysActive obj = new SysActive();
			obj.id= br.ReadUInt32();
			obj.sysName_zh= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.sysName_en= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.active= br.ReadUInt32();
			obj.activityStatus= br.ReadUInt32();
			obj.loadingMethod= br.ReadUInt32();
			obj.startDataTracing= br.ReadUInt32();
			obj.sysModule= br.ReadUInt32();
			obj.moduleVersion= br.ReadUInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 SysActive 数据的列表</returns>
	public static List<SysActive> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 SysActive 所有数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 SysActive 数据列表</returns>
	public static List<SysActive> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.SysActives.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(SysActive).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 SysActive 类中不存在");
				return new List<SysActive>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(SysActive), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<SysActive, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 SysActive 数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getByid(uint id){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListByid(uint id){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 sysName_zh 字段查询单条数据
	/// </summary>
	/// <param name="sysName_zh">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getBysysName_zh(string? sysName_zh){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.sysName_zh == sysName_zh);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysName_zh 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 sysName_zh 字段查询多条数据
	/// </summary>
	/// <param name="sysName_zh">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListBysysName_zh(string? sysName_zh){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.sysName_zh == sysName_zh).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysName_zh 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 sysName_en 字段查询单条数据
	/// </summary>
	/// <param name="sysName_en">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getBysysName_en(string? sysName_en){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.sysName_en == sysName_en);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysName_en 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 sysName_en 字段查询多条数据
	/// </summary>
	/// <param name="sysName_en">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListBysysName_en(string? sysName_en){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.sysName_en == sysName_en).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysName_en 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 active 字段查询单条数据
	/// </summary>
	/// <param name="active">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getByactive(uint? active){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.active == active);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 active 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 active 字段查询多条数据
	/// </summary>
	/// <param name="active">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListByactive(uint? active){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.active == active).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 active 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 activityStatus 字段查询单条数据
	/// </summary>
	/// <param name="activityStatus">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getByactivityStatus(uint? activityStatus){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.activityStatus == activityStatus);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 activityStatus 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 activityStatus 字段查询多条数据
	/// </summary>
	/// <param name="activityStatus">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListByactivityStatus(uint? activityStatus){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.activityStatus == activityStatus).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 activityStatus 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 loadingMethod 字段查询单条数据
	/// </summary>
	/// <param name="loadingMethod">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getByloadingMethod(uint? loadingMethod){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.loadingMethod == loadingMethod);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 loadingMethod 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 loadingMethod 字段查询多条数据
	/// </summary>
	/// <param name="loadingMethod">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListByloadingMethod(uint? loadingMethod){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.loadingMethod == loadingMethod).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 loadingMethod 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 startDataTracing 字段查询单条数据
	/// </summary>
	/// <param name="startDataTracing">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getBystartDataTracing(uint? startDataTracing){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.startDataTracing == startDataTracing);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startDataTracing 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 startDataTracing 字段查询多条数据
	/// </summary>
	/// <param name="startDataTracing">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListBystartDataTracing(uint? startDataTracing){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.startDataTracing == startDataTracing).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startDataTracing 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 sysModule 字段查询单条数据
	/// </summary>
	/// <param name="sysModule">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getBysysModule(uint? sysModule){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.sysModule == sysModule);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysModule 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 sysModule 字段查询多条数据
	/// </summary>
	/// <param name="sysModule">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListBysysModule(uint? sysModule){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.sysModule == sysModule).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 sysModule 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}

	/// <summary>
	/// 根据 moduleVersion 字段查询单条数据
	/// </summary>
	/// <param name="moduleVersion">查询值</param>
	/// <returns>返回匹配的第一条 SysActive 数据，如果没有找到则返回 null</returns>
	public static SysActive getBymoduleVersion(uint? moduleVersion){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.FirstOrDefault(x => x.moduleVersion == moduleVersion);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 moduleVersion 查询 SysActive 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 moduleVersion 字段查询多条数据
	/// </summary>
	/// <param name="moduleVersion">查询值</param>
	/// <returns>返回匹配的所有 SysActive 数据列表</returns>
	public static List<SysActive> getListBymoduleVersion(uint? moduleVersion){
		try{
			var db = ORMTables.Instance;
			return db.SysActives.Where(x => x.moduleVersion == moduleVersion).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 moduleVersion 查询 SysActive 多条数据时发生错误: {ex.Message}");
			return new List<SysActive>();
		}
	}
}


/// <summary>
/// TimeEvent
/// </summary>
[Table("timeevents")]
public class TimeEvent
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 任务名称
	/// </summary>
	public string? eventName
	{
		get;set;
	}

	/// <summary>
	/// 任务实际开始时间
	/// </summary>
	public ulong? taskstartTime
	{
		get;set;
	}

	/// <summary>
	/// 任务结束时间
	/// </summary>
	public ulong? taskEndTime
	{
		get;set;
	}

	/// <summary>
	/// 上次开始时间
	/// </summary>
	public ulong? lastStartTime
	{
		get;set;
	}

	/// <summary>
	/// 上次结束时间
	/// </summary>
	public ulong? lastEndTime
	{
		get;set;
	}

	/// <summary>
	/// 默认时间是0也就是utc时间
	/// </summary>
	public int? serverTimeZone
	{
		get;set;
	}

	/// <summary>
	/// 重复循环次数，0就是无限次循环
	/// </summary>
	public int? LoopCount
	{
		get;set;
	}

	/// <summary>
	/// 已经重复的次数
	/// </summary>
	public int? LoopTimers
	{
		get;set;
	}

	/// <summary>
	/// 第一次是否有cd
	/// </summary>
	public bool isFristNoCD
	{
		get;set;
	}

	/// <summary>
	/// 任务间隔执行时间
	/// </summary>
	public ulong? taskLoopTime
	{
		get;set;
	}

	/// <summary>
	/// 	/// 循环类型
	/// 1.日循环
	/// 2.周循环
	/// 3.月循环

	/// </summary>
	public byte? timeType
	{
		get;set;
	}

	/// <summary>
	/// 每天任务开始的时间，和loopTime共同执行
	/// </summary>
	public ulong? startTime
	{
		get;set;
	}

	/// <summary>
	/// 每天任务开始的时间的结束时间
	/// </summary>
	public ulong? startLimitTime
	{
		get;set;
	}

	/// <summary>
	/// 前置任务id，可以组成任务集合
	/// </summary>
	public string predecessorTaskID
	{
		get;set;
	}

	/// <summary>
	/// 任务的回调事件名字
	/// </summary>
	public string? taskEventString
	{
		get;set;
	}

	/// <summary>
	/// 任务执行日志列表
	/// </summary>
	public string? taskEventLog
	{
		get;set;
	}

	/// <summary>
	/// 	/// 任务目前状态 
	/// 1正在执行，
	/// 2执行错误，
	/// 3执行成功
	/// 

	/// </summary>
	public int? taskState
	{
		get;set;
	}

	/// <summary>
	/// 	/// 任务之前的执行状态
	/// 1正在执行，
	/// 2执行错误，
	/// 3执行成功，
	/// 注意写任务的一定要注意可能服务器被中断的情况

	/// </summary>
	public int? taskPreviousState
	{
		get;set;
	}

	/// <summary>
	/// 	/// 繁忙时间段
	/// 如上次的执行有问题
	/// 在此时间段内不执行

	/// </summary>
	public string? busyTime
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/TimeEvent.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			eventName = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			eventName = null;
		if(raw_data[i][2].ToString() != "")
			taskstartTime = (ulong)Convert.ChangeType(raw_data[i][2].ToString(), typeof(ulong));
		else
			taskstartTime = null;
		if(raw_data[i][3].ToString() != "")
			taskEndTime = (ulong)Convert.ChangeType(raw_data[i][3].ToString(), typeof(ulong));
		else
			taskEndTime = null;
		if(raw_data[i][4].ToString() != "")
			lastStartTime = (ulong)Convert.ChangeType(raw_data[i][4].ToString(), typeof(ulong));
		else
			lastStartTime = null;
		if(raw_data[i][5].ToString() != "")
			lastEndTime = (ulong)Convert.ChangeType(raw_data[i][5].ToString(), typeof(ulong));
		else
			lastEndTime = null;
		if(raw_data[i][6].ToString() != "")
			serverTimeZone = (int)Convert.ChangeType(raw_data[i][6].ToString(), typeof(int));
		else
			serverTimeZone = null;
		if(raw_data[i][7].ToString() != "")
			LoopCount = (int)Convert.ChangeType(raw_data[i][7].ToString(), typeof(int));
		else
			LoopCount = null;
		if(raw_data[i][8].ToString() != "")
			LoopTimers = (int)Convert.ChangeType(raw_data[i][8].ToString(), typeof(int));
		else
			LoopTimers = null;
		if(raw_data[i][9].ToString() != "")
			isFristNoCD = (bool)Convert.ChangeType(raw_data[i][9].ToString(), typeof(bool));
		if(raw_data[i][10].ToString() != "")
			taskLoopTime = (ulong)Convert.ChangeType(raw_data[i][10].ToString(), typeof(ulong));
		else
			taskLoopTime = null;
		if(raw_data[i][11].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][11].ToString(), typeof(byte));
		else
			timeType = null;
		if(raw_data[i][12].ToString() != "")
			startTime = (ulong)Convert.ChangeType(raw_data[i][12].ToString(), typeof(ulong));
		else
			startTime = null;
		if(raw_data[i][13].ToString() != "")
			startLimitTime = (ulong)Convert.ChangeType(raw_data[i][13].ToString(), typeof(ulong));
		else
			startLimitTime = null;
		if(raw_data[i][14].ToString() != "")
			predecessorTaskID = (string)Convert.ChangeType(raw_data[i][14].ToString(), typeof(string));
		if(raw_data[i][15].ToString() != "")
			taskEventString = (string)Convert.ChangeType(raw_data[i][15].ToString(), typeof(string));
		else
			taskEventString = null;
		if(raw_data[i][16].ToString() != "")
			taskEventLog = (string)Convert.ChangeType(raw_data[i][16].ToString(), typeof(string));
		else
			taskEventLog = null;
		if(raw_data[i][17].ToString() != "")
			taskState = (int)Convert.ChangeType(raw_data[i][17].ToString(), typeof(int));
		else
			taskState = null;
		if(raw_data[i][18].ToString() != "")
			taskPreviousState = (int)Convert.ChangeType(raw_data[i][18].ToString(), typeof(int));
		else
			taskPreviousState = null;
		if(raw_data[i][19].ToString() != "")
			busyTime = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
		else
			busyTime = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"eventName", "string"},
		{"taskstartTime", "ulong"},
		{"taskEndTime", "ulong"},
		{"lastStartTime", "ulong"},
		{"lastEndTime", "ulong"},
		{"serverTimeZone", "int"},
		{"LoopCount", "int"},
		{"LoopTimers", "int"},
		{"isFristNoCD", "bool"},
		{"taskLoopTime", "ulong"},
		{"timeType", "byte"},
		{"startTime", "ulong"},
		{"startLimitTime", "ulong"},
		{"predecessorTaskID", "string"},
		{"taskEventString", "string"},
		{"taskEventLog", "string"},
		{"taskState", "int"},
		{"taskPreviousState", "int"},
		{"busyTime", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "eventName=" + eventName + "taskstartTime=" + taskstartTime + "taskEndTime=" + taskEndTime + "lastStartTime=" + lastStartTime + "lastEndTime=" + lastEndTime + "serverTimeZone=" + serverTimeZone + "LoopCount=" + LoopCount + "LoopTimers=" + LoopTimers + "isFristNoCD=" + isFristNoCD + "taskLoopTime=" + taskLoopTime + "timeType=" + timeType + "startTime=" + startTime + "startLimitTime=" + startLimitTime + "predecessorTaskID=" + predecessorTaskID + "taskEventString=" + taskEventString + "taskEventLog=" + taskEventLog + "taskState=" + taskState + "taskPreviousState=" + taskPreviousState + "busyTime=" + busyTime;
	}
	public static string[] propList = { "id","eventName","taskstartTime","taskEndTime","lastStartTime","lastEndTime","serverTimeZone","LoopCount","LoopTimers","isFristNoCD","taskLoopTime","timeType","startTime","startLimitTime","predecessorTaskID","taskEventString","taskEventLog","taskState","taskPreviousState","busyTime", };
	public static TimeEvent add(TimeEvent a, TimeEvent b, uint start, uint end, TimeEvent limit = null) {
		if(a == null || b == null) return null;
		TimeEvent result = new TimeEvent();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static TimeEvent sub(TimeEvent a, TimeEvent b, uint start, uint end) {
		if(a == null || b == null) return null;
		TimeEvent result = new TimeEvent();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(TimeEvent a, TimeEvent b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static TimeEvent max(TimeEvent a, TimeEvent b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static TimeEvent json(TimeEvent a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static TimeEvent setProperty(TimeEvent a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<TimeEvent> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/TimeEvent.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("TimeEvent的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<TimeEvent> list = new List<TimeEvent>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			TimeEvent obj = new TimeEvent();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.eventName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskstartTime= br.ReadUInt64();
			obj.taskEndTime= br.ReadUInt64();
			obj.lastStartTime= br.ReadUInt64();
			obj.lastEndTime= br.ReadUInt64();
			obj.serverTimeZone= br.ReadInt32();
			obj.LoopCount= br.ReadInt32();
			obj.LoopTimers= br.ReadInt32();
			obj.isFristNoCD= br.ReadBoolean();
			obj.taskLoopTime= br.ReadUInt64();
			obj.timeType= br.ReadByte();
			obj.startTime= br.ReadUInt64();
			obj.startLimitTime= br.ReadUInt64();
			obj.predecessorTaskID= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskEventString= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskEventLog= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskState= br.ReadInt32();
			obj.taskPreviousState= br.ReadInt32();
			obj.busyTime= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 TimeEvent 数据的列表</returns>
	public static List<TimeEvent> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 TimeEvent 所有数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.TimeEvents.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(TimeEvent).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 TimeEvent 类中不存在");
				return new List<TimeEvent>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(TimeEvent), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<TimeEvent, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 TimeEvent 数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 eventName 字段查询单条数据
	/// </summary>
	/// <param name="eventName">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByeventName(string? eventName){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.eventName == eventName);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 eventName 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 eventName 字段查询多条数据
	/// </summary>
	/// <param name="eventName">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByeventName(string? eventName){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.eventName == eventName).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 eventName 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskstartTime 字段查询单条数据
	/// </summary>
	/// <param name="taskstartTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskstartTime(ulong? taskstartTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskstartTime == taskstartTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskstartTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskstartTime 字段查询多条数据
	/// </summary>
	/// <param name="taskstartTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskstartTime(ulong? taskstartTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskstartTime == taskstartTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskstartTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskEndTime 字段查询单条数据
	/// </summary>
	/// <param name="taskEndTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskEndTime(ulong? taskEndTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskEndTime == taskEndTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEndTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskEndTime 字段查询多条数据
	/// </summary>
	/// <param name="taskEndTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskEndTime(ulong? taskEndTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskEndTime == taskEndTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEndTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 lastStartTime 字段查询单条数据
	/// </summary>
	/// <param name="lastStartTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBylastStartTime(ulong? lastStartTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.lastStartTime == lastStartTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastStartTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 lastStartTime 字段查询多条数据
	/// </summary>
	/// <param name="lastStartTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBylastStartTime(ulong? lastStartTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.lastStartTime == lastStartTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastStartTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 lastEndTime 字段查询单条数据
	/// </summary>
	/// <param name="lastEndTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBylastEndTime(ulong? lastEndTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.lastEndTime == lastEndTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastEndTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 lastEndTime 字段查询多条数据
	/// </summary>
	/// <param name="lastEndTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBylastEndTime(ulong? lastEndTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.lastEndTime == lastEndTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 lastEndTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 serverTimeZone 字段查询单条数据
	/// </summary>
	/// <param name="serverTimeZone">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByserverTimeZone(int? serverTimeZone){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.serverTimeZone == serverTimeZone);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverTimeZone 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 serverTimeZone 字段查询多条数据
	/// </summary>
	/// <param name="serverTimeZone">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByserverTimeZone(int? serverTimeZone){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.serverTimeZone == serverTimeZone).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 serverTimeZone 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 LoopCount 字段查询单条数据
	/// </summary>
	/// <param name="LoopCount">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByLoopCount(int? LoopCount){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.LoopCount == LoopCount);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 LoopCount 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 LoopCount 字段查询多条数据
	/// </summary>
	/// <param name="LoopCount">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByLoopCount(int? LoopCount){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.LoopCount == LoopCount).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 LoopCount 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 LoopTimers 字段查询单条数据
	/// </summary>
	/// <param name="LoopTimers">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByLoopTimers(int? LoopTimers){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.LoopTimers == LoopTimers);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 LoopTimers 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 LoopTimers 字段查询多条数据
	/// </summary>
	/// <param name="LoopTimers">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByLoopTimers(int? LoopTimers){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.LoopTimers == LoopTimers).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 LoopTimers 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 isFristNoCD 字段查询单条数据
	/// </summary>
	/// <param name="isFristNoCD">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getByisFristNoCD(bool isFristNoCD){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.isFristNoCD == isFristNoCD);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 isFristNoCD 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 isFristNoCD 字段查询多条数据
	/// </summary>
	/// <param name="isFristNoCD">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListByisFristNoCD(bool isFristNoCD){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.isFristNoCD == isFristNoCD).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 isFristNoCD 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskLoopTime 字段查询单条数据
	/// </summary>
	/// <param name="taskLoopTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskLoopTime(ulong? taskLoopTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskLoopTime == taskLoopTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskLoopTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskLoopTime 字段查询多条数据
	/// </summary>
	/// <param name="taskLoopTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskLoopTime(ulong? taskLoopTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskLoopTime == taskLoopTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskLoopTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询单条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.timeType == timeType);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询多条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.timeType == timeType).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 startTime 字段查询单条数据
	/// </summary>
	/// <param name="startTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBystartTime(ulong? startTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.startTime == startTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 startTime 字段查询多条数据
	/// </summary>
	/// <param name="startTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBystartTime(ulong? startTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.startTime == startTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 startLimitTime 字段查询单条数据
	/// </summary>
	/// <param name="startLimitTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBystartLimitTime(ulong? startLimitTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.startLimitTime == startLimitTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startLimitTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 startLimitTime 字段查询多条数据
	/// </summary>
	/// <param name="startLimitTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBystartLimitTime(ulong? startLimitTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.startLimitTime == startLimitTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 startLimitTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 predecessorTaskID 字段查询单条数据
	/// </summary>
	/// <param name="predecessorTaskID">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBypredecessorTaskID(string predecessorTaskID){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.predecessorTaskID == predecessorTaskID);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 predecessorTaskID 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 predecessorTaskID 字段查询多条数据
	/// </summary>
	/// <param name="predecessorTaskID">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBypredecessorTaskID(string predecessorTaskID){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.predecessorTaskID == predecessorTaskID).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 predecessorTaskID 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskEventString 字段查询单条数据
	/// </summary>
	/// <param name="taskEventString">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskEventString(string? taskEventString){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskEventString == taskEventString);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEventString 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskEventString 字段查询多条数据
	/// </summary>
	/// <param name="taskEventString">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskEventString(string? taskEventString){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskEventString == taskEventString).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEventString 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskEventLog 字段查询单条数据
	/// </summary>
	/// <param name="taskEventLog">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskEventLog(string? taskEventLog){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskEventLog == taskEventLog);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEventLog 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskEventLog 字段查询多条数据
	/// </summary>
	/// <param name="taskEventLog">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskEventLog(string? taskEventLog){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskEventLog == taskEventLog).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskEventLog 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskState 字段查询单条数据
	/// </summary>
	/// <param name="taskState">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskState(int? taskState){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskState == taskState);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskState 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskState 字段查询多条数据
	/// </summary>
	/// <param name="taskState">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskState(int? taskState){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskState == taskState).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskState 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 taskPreviousState 字段查询单条数据
	/// </summary>
	/// <param name="taskPreviousState">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBytaskPreviousState(int? taskPreviousState){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.taskPreviousState == taskPreviousState);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskPreviousState 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 taskPreviousState 字段查询多条数据
	/// </summary>
	/// <param name="taskPreviousState">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBytaskPreviousState(int? taskPreviousState){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.taskPreviousState == taskPreviousState).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 taskPreviousState 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}

	/// <summary>
	/// 根据 busyTime 字段查询单条数据
	/// </summary>
	/// <param name="busyTime">查询值</param>
	/// <returns>返回匹配的第一条 TimeEvent 数据，如果没有找到则返回 null</returns>
	public static TimeEvent getBybusyTime(string? busyTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.FirstOrDefault(x => x.busyTime == busyTime);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 busyTime 查询 TimeEvent 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 busyTime 字段查询多条数据
	/// </summary>
	/// <param name="busyTime">查询值</param>
	/// <returns>返回匹配的所有 TimeEvent 数据列表</returns>
	public static List<TimeEvent> getListBybusyTime(string? busyTime){
		try{
			var db = ORMTables.Instance;
			return db.TimeEvents.Where(x => x.busyTime == busyTime).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 busyTime 查询 TimeEvent 多条数据时发生错误: {ex.Message}");
			return new List<TimeEvent>();
		}
	}
}


/// <summary>
/// UserVar
/// </summary>
[Table("uservars")]
public class UserVar
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 用户uuid
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 变量名
	/// </summary>
	public string? name
	{
		get;set;
	}

	/// <summary>
	/// 变量类型
	/// </summary>
	public string? type
	{
		get;set;
	}

	/// <summary>
	/// 变量值
	/// </summary>
	public string? value
	{
		get;set;
	}

	/// <summary>
	/// 保存时间类型
	/// </summary>
	public byte? timeType
	{
		get;set;
	}

	/// <summary>
	/// 时间值
	/// </summary>
	public int? timeValue
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/UserVar.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			name = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		else
			name = null;
		if(raw_data[i][2].ToString() != "")
			type = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		else
			type = null;
		if(raw_data[i][3].ToString() != "")
			value = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		else
			value = null;
		if(raw_data[i][4].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][4].ToString(), typeof(byte));
		else
			timeType = null;
		if(raw_data[i][5].ToString() != "")
			timeValue = (int)Convert.ChangeType(raw_data[i][5].ToString(), typeof(int));
		else
			timeValue = null;
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"name", "string"},
		{"type", "string"},
		{"value", "string"},
		{"timeType", "byte"},
		{"timeValue", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "name=" + name + "type=" + type + "value=" + value + "timeType=" + timeType + "timeValue=" + timeValue;
	}
	public static string[] propList = { "id","name","type","value","timeType","timeValue", };
	public static UserVar add(UserVar a, UserVar b, uint start, uint end, UserVar limit = null) {
		if(a == null || b == null) return null;
		UserVar result = new UserVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static UserVar sub(UserVar a, UserVar b, uint start, uint end) {
		if(a == null || b == null) return null;
		UserVar result = new UserVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(UserVar a, UserVar b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static UserVar max(UserVar a, UserVar b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static UserVar json(UserVar a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static UserVar setProperty(UserVar a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<UserVar> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/UserVar.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("UserVar的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<UserVar> list = new List<UserVar>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			UserVar obj = new UserVar();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.name= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.type= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.value= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timeType= br.ReadByte();
			obj.timeValue= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}

	/// <summary>
	/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <returns>返回所有 UserVar 数据的列表</returns>
	public static List<UserVar> getAllDataToList(){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"获取 UserVar 所有数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库
	/// </summary>
	/// <param name="fieldName">字段名称</param>
	/// <param name="value">字段值</param>
	/// <returns>返回匹配条件的 UserVar 数据列表</returns>
	public static List<UserVar> getDataToKey(string fieldName, object value){
		try{
			var db = ORMTables.Instance;
			var query = db.UserVars.AsQueryable();
			
			// 使用反射根据字段名进行查询
			var property = typeof(UserVar).GetProperty(fieldName);
			if (property == null){
				Console.WriteLine($"字段 {fieldName} 在 UserVar 类中不存在");
				return new List<UserVar>();
			}
			
			// 构建 LINQ 表达式进行查询
			var parameter = System.Linq.Expressions.Expression.Parameter(typeof(UserVar), "x");
			var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);
			var constant = System.Linq.Expressions.Expression.Constant(value);
			var equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);
			var lambda = System.Linq.Expressions.Expression.Lambda<Func<UserVar, bool>>(equality, parameter);
			
			return query.Where(lambda).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据字段 {fieldName} 查询 UserVar 数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 id 字段查询单条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.id == id);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 id 字段查询多条数据
	/// </summary>
	/// <param name="id">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListByid(string id){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.id == id).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 id 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 name 字段查询单条数据
	/// </summary>
	/// <param name="name">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getByname(string? name){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.name == name);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 name 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 name 字段查询多条数据
	/// </summary>
	/// <param name="name">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListByname(string? name){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.name == name).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 name 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 type 字段查询单条数据
	/// </summary>
	/// <param name="type">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getBytype(string? type){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.type == type);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 type 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 type 字段查询多条数据
	/// </summary>
	/// <param name="type">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListBytype(string? type){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.type == type).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 type 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 value 字段查询单条数据
	/// </summary>
	/// <param name="value">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getByvalue(string? value){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.value == value);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 value 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 value 字段查询多条数据
	/// </summary>
	/// <param name="value">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListByvalue(string? value){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.value == value).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 value 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询单条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.timeType == timeType);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timeType 字段查询多条数据
	/// </summary>
	/// <param name="timeType">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListBytimeType(byte? timeType){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.timeType == timeType).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeType 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}

	/// <summary>
	/// 根据 timeValue 字段查询单条数据
	/// </summary>
	/// <param name="timeValue">查询值</param>
	/// <returns>返回匹配的第一条 UserVar 数据，如果没有找到则返回 null</returns>
	public static UserVar getBytimeValue(int? timeValue){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.FirstOrDefault(x => x.timeValue == timeValue);
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeValue 查询 UserVar 单条数据时发生错误: {ex.Message}");
			return null;
		}
	}

	/// <summary>
	/// 根据 timeValue 字段查询多条数据
	/// </summary>
	/// <param name="timeValue">查询值</param>
	/// <returns>返回匹配的所有 UserVar 数据列表</returns>
	public static List<UserVar> getListBytimeValue(int? timeValue){
		try{
			var db = ORMTables.Instance;
			return db.UserVars.Where(x => x.timeValue == timeValue).ToList();
		}
		catch (Exception ex){
			Console.WriteLine($"根据 timeValue 查询 UserVar 多条数据时发生错误: {ex.Message}");
			return new List<UserVar>();
		}
	}
}

		/// <summary>
		/// 数据库里的表
		/// 这里的属性如果有修改需要清空数据库, 或者新建一个数据库 (数据库只要是空的就行)
		/// 如果只是表里的数据有修改, 只需要清空对应的表, 重启服务器会重新填数据
		/// </summary>
		public class ORMTables: DBBase
		{
			// 私有静态实例变量
			private static ORMTables _instance;
			// 线程安全锁对象
			private static readonly object _lock = new object();

			/// <summary>
			/// 公共静态属性，用于访问单例实例
			/// </summary>
			public static ORMTables Instance
			{
				get
				{
					// 双重检查锁定模式确保线程安全
					if (_instance == null)
					{
						lock (_lock)
						{
							if (_instance == null)
							{
								_instance = new ORMTables();
							}
						}
					}
					return _instance;
				}
			}

		/// <summary>
		/// Account
		/// <summary>
		public DbSet<Account> Accounts { get; set; }
		/// <summary>
		/// AI_Game_Output
		/// <summary>
		public DbSet<AI_Game_Output> AI_Game_Outputs { get; set; }
		/// <summary>
		/// AI_Game_OutputData
		/// <summary>
		public DbSet<AI_Game_OutputData> AI_Game_OutputDatas { get; set; }
		/// <summary>
		/// ArtAIChat
		/// <summary>
		public DbSet<ArtAIChat_Output> ArtAIChat_Outputs { get; set; }
		/// <summary>
		/// DesignAIChat
		/// <summary>
		public DbSet<DesignAIChat_Output> DesignAIChat_Outputs { get; set; }
		/// <summary>
		/// ErrorInfo
		/// <summary>
		public DbSet<ErrorInfo> ErrorInfos { get; set; }
		/// <summary>
		/// serverVar
		/// <summary>
		public DbSet<serverVar> serverVars { get; set; }
		/// <summary>
		/// SeverConfig
		/// <summary>
		public DbSet<SeverConfigBase> SeverConfigBases { get; set; }
		/// <summary>
		/// SeverData
		/// <summary>
		public DbSet<SeverData> SeverDatas { get; set; }
		/// <summary>
		/// SysActive
		/// <summary>
		public DbSet<SysActive> SysActives { get; set; }
		/// <summary>
		/// TimeEvent
		/// <summary>
		public DbSet<TimeEvent> TimeEvents { get; set; }
		/// <summary>
		/// UserVar
		/// <summary>
		public DbSet<UserVar> UserVars { get; set; }
		/// <summary>
		/// ComfyUIServer
		/// <summary>
		public DbSet<ComfyUIServer> ComfyUIServers { get; set; }
		/// <summary>
		/// ComfyUIWorkflow
		/// <summary>
		public DbSet<ComfyUIWorkflow> ComfyUIWorkflows { get; set; }
		/// <summary>
		/// ComfyUITask
		/// <summary>
		public DbSet<ComfyUITask> ComfyUITasks { get; set; }

		/// <summary>
		/// 读取所有数据到数据库
		/// </summary>
		public void readAllData(){

			var db = ORMTables.Instance;
           try{
                db.Accounts.AddRange(Account.readAllData().ToArray());
			db.AI_Game_Outputs.AddRange(AI_Game_Output.readAllData().ToArray());
			db.AI_Game_OutputDatas.AddRange(AI_Game_OutputData.readAllData().ToArray());
			db.ArtAIChat_Outputs.AddRange(ArtAIChat_Output.readAllData().ToArray());
			db.DesignAIChat_Outputs.AddRange(DesignAIChat_Output.readAllData().ToArray());
			db.ErrorInfos.AddRange(ErrorInfo.readAllData().ToArray());
			db.serverVars.AddRange(serverVar.readAllData().ToArray());
			db.SeverConfigBases.AddRange(SeverConfigBase.readAllData().ToArray());
			db.SeverDatas.AddRange(SeverData.readAllData().ToArray());
			db.SysActives.AddRange(SysActive.readAllData().ToArray());
			db.TimeEvents.AddRange(TimeEvent.readAllData().ToArray());
			db.UserVars.AddRange(UserVar.readAllData().ToArray());
			db.ComfyUIServers.AddRange(ComfyUIServer.readAllData().ToArray());
			db.ComfyUIWorkflows.AddRange(ComfyUIWorkflow.readAllData().ToArray());
			db.ComfyUITasks.AddRange(ComfyUITask.readAllData().ToArray());

			int dbstate=db.SaveChanges();
			db.Dispose();
            }
			catch (Exception ex){
                Console.WriteLine($"清空数据库数据时发生错误: {ex.Message}");
            }
			finally{
                db.Dispose();
            }

            Console.WriteLine("数据上传数据库完毕");
        }
		/// <summary>
		/// 清空数据库中所有表的数据
		/// </summary>
		public void ClearAllData(){
			var db = ORMTables.Instance;
			try{
				db.Accounts.ExecuteDelete();
				db.AI_Game_Outputs.ExecuteDelete();
				db.AI_Game_OutputDatas.ExecuteDelete();
				db.ArtAIChat_Outputs.ExecuteDelete();
				db.DesignAIChat_Outputs.ExecuteDelete();
				db.ErrorInfos.ExecuteDelete();
				db.serverVars.ExecuteDelete();
				db.SeverConfigBases.ExecuteDelete();
				db.SeverDatas.ExecuteDelete();
				db.SysActives.ExecuteDelete();
				db.TimeEvents.ExecuteDelete();
				db.UserVars.ExecuteDelete();
				db.ComfyUIServers.ExecuteDelete();
				db.ComfyUIWorkflows.ExecuteDelete();
				db.ComfyUITasks.ExecuteDelete();

				db.SaveChanges();
				Console.WriteLine("所有数据库表数据已清空");
			}
			catch (Exception ex){
				Console.WriteLine($"清空数据库数据时发生错误: {ex.Message}");
			}
			finally{
				db.Dispose();
			}
		}
		}
}
